"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/employees/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/employees/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/employees/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmployeesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/touch-friendly */ \"(app-pages-browser)/./src/components/ui/touch-friendly.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_employees_EmployeeForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/employees/EmployeeForm */ \"(app-pages-browser)/./src/components/employees/EmployeeForm.tsx\");\n/* harmony import */ var _components_employees_EmployeeProfile__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/employees/EmployeeProfile */ \"(app-pages-browser)/./src/components/employees/EmployeeProfile.tsx\");\n/* harmony import */ var _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/employeeApi */ \"(app-pages-browser)/./src/lib/api/employeeApi.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Note: All employee data now comes from the database via API calls\n// Mock data has been removed and replaced with real database integration\n// Filter options for UI dropdowns\nconst statuses = [\n    \"All\",\n    \"active\",\n    \"on-leave\",\n    \"inactive\"\n];\nfunction EmployeesPage() {\n    _s();\n    // Data state\n    const [employees, setEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [positions, setPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [managers, setManagers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalEmployees, setTotalEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Filter and search state\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"firstName\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    const [selectedEmployees, setSelectedEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAdvancedFilters, setShowAdvancedFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [performanceFilter, setPerformanceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [salaryRange, setSalaryRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        200000\n    ]);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBulkActions, setShowBulkActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal state\n    const [showEmployeeForm, setShowEmployeeForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEmployeeProfile, setShowEmployeeProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEmployeeId, setSelectedEmployeeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEmployee, setEditingEmployee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmployeesPage.useEffect\": ()=>{\n            loadInitialData();\n        }\n    }[\"EmployeesPage.useEffect\"], []);\n    // Load employees when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmployeesPage.useEffect\": ()=>{\n            loadEmployees();\n        }\n    }[\"EmployeesPage.useEffect\"], [\n        searchQuery,\n        selectedDepartment,\n        selectedStatus,\n        sortBy,\n        sortOrder,\n        currentPage\n    ]);\n    const loadInitialData = async ()=>{\n        setLoading(true);\n        try {\n            const [deptResponse, posResponse, mgrResponse] = await Promise.all([\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getDepartments(),\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getPositions(),\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getManagers()\n            ]);\n            const deptData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(deptResponse);\n            const posData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(posResponse);\n            const mgrData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(mgrResponse);\n            if (deptData) setDepartments(deptData);\n            if (posData) setPositions(posData);\n            if (mgrData) setManagers(mgrData);\n            await loadEmployees();\n        } catch (error) {\n            console.error(\"Failed to load initial data:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load employee data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadEmployees = async ()=>{\n        const filters = {\n            page: currentPage,\n            limit: 20,\n            search: searchQuery || undefined,\n            department: selectedDepartment !== \"All\" ? selectedDepartment : undefined,\n            status: selectedStatus !== \"All\" ? selectedStatus.toLowerCase() : undefined,\n            sortBy: sortBy,\n            sortOrder: sortOrder\n        };\n        const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getEmployees(filters);\n        const data = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response);\n        if (data) {\n            setEmployees(data.employees);\n            setTotalEmployees(data.total);\n            setTotalPages(data.totalPages);\n        }\n    };\n    // Note: Filtering and sorting is now handled by the backend API\n    // The employees array already contains the filtered and sorted results\n    const filteredAndSortedEmployees = employees;\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"success\";\n            case \"on-leave\":\n                return \"warning\";\n            case \"inactive\":\n                return \"destructive\";\n            default:\n                return \"secondary\";\n        }\n    };\n    const getPerformanceColor = (rating)=>{\n        if (rating >= 4.5) return \"text-green-600 dark:text-green-400\";\n        if (rating >= 4.0) return \"text-blue-600 dark:text-blue-400\";\n        if (rating >= 3.5) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    const handleSelectAll = ()=>{\n        if (selectedEmployees.length === filteredAndSortedEmployees.length) {\n            setSelectedEmployees([]);\n        } else {\n            setSelectedEmployees(filteredAndSortedEmployees.map((emp)=>emp.id));\n        }\n    };\n    const handleSelectEmployee = (id)=>{\n        setSelectedEmployees((prev)=>prev.includes(id) ? prev.filter((empId)=>empId !== id) : [\n                ...prev,\n                id\n            ]);\n    };\n    const handleViewEmployee = (employeeId)=>{\n        setSelectedEmployeeId(employeeId);\n        setShowEmployeeProfile(true);\n    };\n    const handleEditEmployee = (employee)=>{\n        setEditingEmployee(employee);\n        setShowEmployeeForm(true);\n    };\n    const handleAddEmployee = ()=>{\n        setEditingEmployee(null);\n        setShowEmployeeForm(true);\n    };\n    const handleDeleteEmployee = async (employeeId)=>{\n        if (confirm(\"Are you sure you want to delete this employee?\")) {\n            const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.deleteEmployee(employeeId);\n            if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee deleted successfully\")) {\n                await loadEmployees();\n            }\n        }\n    };\n    const handleBulkAction = async (action)=>{\n        try {\n            switch(action){\n                case 'delete':\n                    if (confirm(\"Are you sure you want to delete \".concat(selectedEmployees.length, \" employees?\"))) {\n                        // Implement bulk delete\n                        sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"\".concat(selectedEmployees.length, \" employees deleted\"));\n                    }\n                    break;\n                case 'export':\n                    const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.exportEmployees({\n                        search: selectedEmployees.join(',')\n                    });\n                    if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Export started\")) {\n                    // Handle export\n                    }\n                    break;\n                default:\n                    console.log(\"Performing \".concat(action, \" on employees:\"), selectedEmployees);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Bulk action failed\");\n        } finally{\n            setSelectedEmployees([]);\n            setShowBulkActions(false);\n        }\n    };\n    const toggleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortBy(field);\n            setSortOrder(\"asc\");\n        }\n    };\n    const handleRefresh = async ()=>{\n        await (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.withLoading)(loadEmployees, setIsRefreshing);\n    };\n    const handleSwipeLeft = (employeeId)=>{\n        handleDeleteEmployee(employeeId);\n    };\n    const handleSwipeRight = (employeeId)=>{\n        // Implement archive functionality\n        sonner__WEBPACK_IMPORTED_MODULE_11__.toast.info(\"Archive functionality coming soon\");\n    };\n    const handleEmployeeFormSubmit = async (data)=>{\n        try {\n            if (editingEmployee) {\n                const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.updateEmployee(editingEmployee.id, data);\n                if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee updated successfully\")) {\n                    setShowEmployeeForm(false);\n                    setEditingEmployee(null);\n                    await loadEmployees();\n                }\n            } else {\n                const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.createEmployee(data);\n                if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee created successfully\")) {\n                    setShowEmployeeForm(false);\n                    await loadEmployees();\n                }\n            }\n        } catch (error) {\n            console.error(\"Form submission error:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to save employee\");\n        }\n    };\n    const handleCloseForm = ()=>{\n        setShowEmployeeForm(false);\n        setEditingEmployee(null);\n    };\n    const handleCloseProfile = ()=>{\n        setShowEmployeeProfile(false);\n        setSelectedEmployeeId(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.PullToRefresh, {\n        onRefresh: handleRefresh,\n        className: \"flex-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_13__.Header, {\n                    title: \"Employee Management\",\n                    subtitle: \"Managing \".concat(totalEmployees, \" employees across \").concat(departments.length - 1, \" departments\"),\n                    actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            selectedEmployees.length,\n                                            \" selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowBulkActions(!showBulkActions),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            \"Bulk Actions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.TouchButton, {\n                                variant: \"secondary\",\n                                size: \"sm\",\n                                onClick: handleRefresh,\n                                disabled: isRefreshing,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2 \".concat(isRefreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    isRefreshing ? 'Refreshing...' : 'Refresh'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                onClick: handleAddEmployee,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    \"Add Employee\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 7\n                }, this),\n                showBulkActions && selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-blue-900\",\n                                        children: [\n                                            \"Bulk Actions for \",\n                                            selectedEmployees.length,\n                                            \" employees:\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('send-email'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Send Email\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('export-data'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export Data\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('update-status'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Update Status\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setShowBulkActions(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Search employees by name, email, position, or department...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedDepartment,\n                                                onChange: (e)=>setSelectedDepartment(e.target.value),\n                                                className: \"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                \"aria-label\": \"Filter by department\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"All\",\n                                                        children: \"All Departments\"\n                                                    }, \"all\", false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: dept.name,\n                                                            children: [\n                                                                dept.name,\n                                                                \" Department\"\n                                                            ]\n                                                        }, dept.id, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                \"aria-label\": \"Filter by status\",\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === \"All\" ? \"All Status\" : status.charAt(0).toUpperCase() + status.slice(1)\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: ()=>setShowAdvancedFilters(!showAdvancedFilters),\n                                                className: showAdvancedFilters ? \"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800\" : \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: ()=>toggleSort(sortBy),\n                                                children: sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 40\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 74\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            showAdvancedFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: \"auto\"\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Sort By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    \"aria-label\": \"Sort by field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"department\",\n                                                            children: \"Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"performance\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"startDate\",\n                                                            children: \"Start Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Performance Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: performanceFilter,\n                                                    onChange: (e)=>setPerformanceFilter(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    \"aria-label\": \"Filter by performance level\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: \"All Performance Levels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"high\",\n                                                            children: \"High (4.5+)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"medium\",\n                                                            children: \"Medium (3.5-4.4)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"low\",\n                                                            children: \"Low (<3.5)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Salary Range\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            type: \"number\",\n                                                            placeholder: \"Min\",\n                                                            value: salaryRange[0],\n                                                            onChange: (e)=>setSalaryRange([\n                                                                    parseInt(e.target.value) || 0,\n                                                                    salaryRange[1]\n                                                                ]),\n                                                            className: \"w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            type: \"number\",\n                                                            placeholder: \"Max\",\n                                                            value: salaryRange[1],\n                                                            onChange: (e)=>setSalaryRange([\n                                                                    salaryRange[0],\n                                                                    parseInt(e.target.value) || 200000\n                                                                ]),\n                                                            className: \"w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Employees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.filter((emp)=>emp.status === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-8 w-8 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.filter((emp)=>emp.status === \"on-leave\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"On Leave\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600 dark:text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: (employees.reduce((sum, emp)=>sum + emp.performance, 0) / employees.length).toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Avg Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredAndSortedEmployees.length,\n                                        \" of \",\n                                        employees.length,\n                                        \" employees\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 11\n                                }, this),\n                                selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                selectedEmployees.length,\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedEmployees([]),\n                                            className: \"text-blue-600 hover:text-blue-700\",\n                                            children: \"Clear selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: viewMode === \"grid\" ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewMode(\"grid\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"Grid\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: viewMode === \"list\" ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewMode(\"list\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"List\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 7\n                }, this),\n                viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: filteredAndSortedEmployees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.SwipeableCard, {\n                                onSwipeLeft: ()=>handleSwipeLeft(employee.id),\n                                onSwipeRight: ()=>handleSwipeRight(employee.id),\n                                className: \"hover:shadow-lg transition-all cursor-pointer \".concat(selectedEmployees.includes(employee.id) ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"border-0 shadow-none bg-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: selectedEmployees.includes(employee.id),\n                                                                        onChange: ()=>handleSelectEmployee(employee.id),\n                                                                        className: \"absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                                        \"aria-label\": \"Select \".concat(employee.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                        className: \"h-12 w-12 ml-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                                src: employee.avatar,\n                                                                                alt: employee.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                                children: employee.name.split(' ').map((n)=>n[0]).join('')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-foreground\",\n                                                                        children: employee.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: employee.position\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: getStatusColor(employee.status),\n                                                            children: employee.status.replace('-', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-4 w-4 \".concat(getPerformanceColor(employee.performance))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium \".concat(getPerformanceColor(employee.performance)),\n                                                                    children: employee.performance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"truncate\",\n                                                                    children: employee.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: employee.department\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: employee.location\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Started \",\n                                                                        new Date(employee.startDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        employee.salary.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 15\n                            }, this)\n                        }, employee.id, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this),\n                viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-muted/50 border-b border-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectedEmployees.length === filteredAndSortedEmployees.length && filteredAndSortedEmployees.length > 0,\n                                                        onChange: handleSelectAll,\n                                                        className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                        \"aria-label\": \"Select all employees\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"name\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Employee\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"name\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 70\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 104\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Position\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"department\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"department\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 76\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 110\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"performance\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Performance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"performance\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 77\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 111\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Salary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: filteredAndSortedEmployees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.tr, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: index * 0.05\n                                                },\n                                                className: \"border-b border-border hover:bg-muted/50 \".concat(selectedEmployees.includes(employee.id) ? 'bg-blue-50 dark:bg-blue-950/20' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedEmployees.includes(employee.id),\n                                                            onChange: ()=>handleSelectEmployee(employee.id),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                            \"aria-label\": \"Select \".concat(employee.name)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                    className: \"h-10 w-10\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                            src: employee.avatar,\n                                                                            alt: employee.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                            children: employee.name.split(' ').map((n)=>n[0]).join('')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-foreground\",\n                                                                            children: employee.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: employee.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-foreground\",\n                                                        children: employee.position\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-muted-foreground\",\n                                                        children: employee.department\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: getStatusColor(employee.status),\n                                                            children: employee.status.replace('-', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-4 w-4 \".concat(getPerformanceColor(employee.performance))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(getPerformanceColor(employee.performance)),\n                                                                    children: employee.performance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-foreground font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            employee.salary.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8 text-red-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, employee.id, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.FloatingActionButton, {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 822,\n                        columnNumber: 15\n                    }, void 0),\n                    onClick: handleAddEmployee,\n                    position: \"bottom-right\",\n                    className: \"lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                    open: showEmployeeForm,\n                    onOpenChange: setShowEmployeeForm,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                        className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                    children: editingEmployee ? \"Edit Employee\" : \"Add New Employee\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employees_EmployeeForm__WEBPACK_IMPORTED_MODULE_8__.EmployeeForm, {\n                                employee: editingEmployee,\n                                onSubmit: handleEmployeeFormSubmit,\n                                onCancel: handleCloseForm,\n                                departments: departments.filter((d)=>d.id !== \"All\"),\n                                positions: positions,\n                                managers: managers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                    open: showEmployeeProfile,\n                    onOpenChange: setShowEmployeeProfile,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                        className: \"max-w-6xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                    children: \"Employee Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 11\n                            }, this),\n                            selectedEmployeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employees_EmployeeProfile__WEBPACK_IMPORTED_MODULE_9__.EmployeeProfile, {\n                                employeeId: selectedEmployeeId,\n                                onEdit: ()=>{\n                                    const employee = employees.find((e)=>e.id === selectedEmployeeId);\n                                    if (employee) {\n                                        setShowEmployeeProfile(false);\n                                        handleEditEmployee(employee);\n                                    }\n                                },\n                                onClose: handleCloseProfile\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 7\n                }, this),\n                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                className: \"h-6 w-6 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Loading employees...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n            lineNumber: 290,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n}\n_s(EmployeesPage, \"u9PT6KCP1KjFfDnh2VpxQXPmpFY=\");\n_c = EmployeesPage;\nvar _c;\n$RefreshReg$(_c, \"EmployeesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/employees/page.tsx\n"));

/***/ })

});