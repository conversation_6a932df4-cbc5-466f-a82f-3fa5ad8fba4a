const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function checkAwadeshRole() {
  console.log('🔍 Checking awadhesh user role in database...');
  console.log('=' .repeat(50));
  
  try {
    // Check user role in users table
    const userQuery = `
      SELECT 
        id, 
        email, 
        role, 
        is_active,
        created_at,
        updated_at
      FROM users 
      WHERE email = '<EMAIL>'
    `;
    
    const userResult = await pool.query(userQuery);
    
    if (userResult.rows.length === 0) {
      console.log('❌ awadhesh user not found in database');
      return;
    }
    
    const user = userResult.rows[0];
    console.log('📋 User Information:');
    console.log('   ID:', user.id);
    console.log('   Email:', user.email);
    console.log('   Role:', user.role);
    console.log('   Active:', user.is_active);
    console.log('   Created:', user.created_at);
    console.log('   Updated:', user.updated_at);
    
    // Check user roles table
    console.log('\n🔐 Checking user_roles table...');
    const userRolesQuery = `
      SELECT
        ur.user_id,
        ur.role_id,
        r.name as role_name,
        ur.assigned_at
      FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = $1
    `;

    const userRolesResult = await pool.query(userRolesQuery, [user.id]);

    if (userRolesResult.rows.length === 0) {
      console.log('   ⚠️  No roles found in user_roles table');
    } else {
      console.log('   📝 Assigned Roles:');
      userRolesResult.rows.forEach(role => {
        console.log(`     - ${role.role_name}`);
        console.log(`       Assigned: ${role.assigned_at}`);
      });
    }
    
    // Check user permissions
    console.log('\n🛡️  Checking user permissions...');
    const permissionsQuery = `
      SELECT DISTINCT p.name as permission_name
      FROM user_permissions up
      JOIN permissions p ON up.permission_id = p.id
      WHERE up.user_id = $1

      UNION

      SELECT DISTINCT p.name as permission_name
      FROM user_roles ur
      JOIN role_permissions rp ON ur.role_id = rp.role_id
      JOIN permissions p ON rp.permission_id = p.id
      WHERE ur.user_id = $1

      ORDER BY permission_name
    `;

    const permissionsResult = await pool.query(permissionsQuery, [user.id]);

    if (permissionsResult.rows.length === 0) {
      console.log('   ⚠️  No permissions found');
    } else {
      console.log(`   📋 Total Permissions: ${permissionsResult.rows.length}`);
      permissionsResult.rows.forEach(perm => {
        console.log(`     - ${perm.permission_name}`);
      });
    }
    
    // Check what the backend login would return
    console.log('\n🔄 Simulating backend login response...');
    const axios = require('axios');
    
    try {
      const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
        email: '<EMAIL>',
        password: 'awadhesh123'
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Backend Login Response:');
      console.log('   User ID:', loginResponse.data.user.id);
      console.log('   Email:', loginResponse.data.user.email);
      console.log('   Role:', loginResponse.data.user.role);
      console.log('   Permissions:', loginResponse.data.user.permissions.length, 'permissions');
      console.log('   Permission List:', loginResponse.data.user.permissions);
      
    } catch (loginError) {
      console.log('❌ Backend login failed:', loginError.response?.data || loginError.message);
    }
    
    console.log('\n🎯 DIAGNOSIS:');
    if (user.role === 'super_admin') {
      console.log('✅ Database role is correct: super_admin');
      console.log('🔍 Issue might be in frontend role display logic');
    } else {
      console.log('❌ Database role is incorrect:', user.role);
      console.log('🔧 Need to update database role to super_admin');
    }
    
  } catch (error) {
    console.error('❌ Error checking awadhesh role:', error);
  } finally {
    await pool.end();
  }
}

checkAwadeshRole();
