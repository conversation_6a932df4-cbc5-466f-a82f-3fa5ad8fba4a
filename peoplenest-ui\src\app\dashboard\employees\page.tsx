"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Search,
  Filter,
  Plus,
  Download,
  MoreHorizontal,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Star,
  Edit,
  Trash2,
  Eye,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Users,
  UserCheck,
  UserX,
  Clock,
  DollarSign,
  TrendingUp,
  FileText,
  Send,
  CheckSquare,
  X,
  RefreshCw,
  Settings,
  Loader2,
  AlertCircle
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { TouchButton, SwipeableCard, PullToRefresh, FloatingActionButton } from "@/components/ui/touch-friendly"
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { EmployeeForm } from "@/components/employees/EmployeeForm"
import { EmployeeProfile } from "@/components/employees/EmployeeProfile"
import { employeeApi, type Employee, type EmployeeFilters, handleApiResponse, withLoading } from "@/lib/api/employeeApi"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"

// Note: All employee data now comes from the database via API calls
// Mock data has been removed and replaced with real database integration

// Filter options for UI dropdowns
const statuses = ["All", "active", "on-leave", "inactive"]

export default function EmployeesPage() {
  // Data state
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<Array<{ id: string; name: string }>>([])
  const [positions, setPositions] = useState<Array<{ id: string; title: string; departmentId: string }>>([])
  const [managers, setManagers] = useState<Array<{ id: string; name: string; departmentId: string }>>([])
  const [loading, setLoading] = useState(true)
  const [totalEmployees, setTotalEmployees] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState<"firstName" | "department" | "hireDate">("firstName")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [performanceFilter, setPerformanceFilter] = useState<"all" | "high" | "medium" | "low">("all")
  const [salaryRange, setSalaryRange] = useState<[number, number]>([0, 200000])
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Modal state
  const [showEmployeeForm, setShowEmployeeForm] = useState(false)
  const [showEmployeeProfile, setShowEmployeeProfile] = useState(false)
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null)
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Load employees when filters change
  useEffect(() => {
    loadEmployees()
  }, [searchQuery, selectedDepartment, selectedStatus, sortBy, sortOrder, currentPage])

  const loadInitialData = async () => {
    setLoading(true)
    try {
      const [deptResponse, posResponse, mgrResponse] = await Promise.all([
        employeeApi.getDepartments(),
        employeeApi.getPositions(),
        employeeApi.getManagers()
      ])

      const deptData = handleApiResponse(deptResponse)
      const posData = handleApiResponse(posResponse)
      const mgrData = handleApiResponse(mgrResponse)

      if (deptData) setDepartments(deptData)
      if (posData) setPositions(posData)
      if (mgrData) setManagers(mgrData)

      await loadEmployees()
    } catch (error) {
      console.error("Failed to load initial data:", error)
      toast.error("Failed to load employee data")
    } finally {
      setLoading(false)
    }
  }

  const loadEmployees = async () => {
    const filters: EmployeeFilters = {
      page: currentPage,
      limit: 20,
      search: searchQuery || undefined,
      department: selectedDepartment !== "All" ? selectedDepartment : undefined,
      status: selectedStatus !== "All" ? selectedStatus.toLowerCase() as any : undefined,
      sortBy: sortBy,
      sortOrder: sortOrder
    }

    const response = await employeeApi.getEmployees(filters)
    const data = handleApiResponse(response)

    if (data) {
      setEmployees(data.employees)
      setTotalEmployees(data.total)
      setTotalPages(data.totalPages)
    }
  }

  // Note: Filtering and sorting is now handled by the backend API
  // The employees array already contains the filtered and sorted results
  const filteredAndSortedEmployees = employees

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "success"
      case "on-leave": return "warning"
      case "inactive": return "destructive"
      default: return "secondary"
    }
  }

  const getPerformanceColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600 dark:text-green-400"
    if (rating >= 4.0) return "text-blue-600 dark:text-blue-400"
    if (rating >= 3.5) return "text-yellow-600 dark:text-yellow-400"
    return "text-red-600 dark:text-red-400"
  }

  const handleSelectAll = () => {
    if (selectedEmployees.length === filteredAndSortedEmployees.length) {
      setSelectedEmployees([])
    } else {
      setSelectedEmployees(filteredAndSortedEmployees.map(emp => emp.id))
    }
  }

  const handleSelectEmployee = (id: string) => {
    setSelectedEmployees(prev =>
      prev.includes(id)
        ? prev.filter(empId => empId !== id)
        : [...prev, id]
    )
  }

  const handleViewEmployee = (employeeId: string) => {
    setSelectedEmployeeId(employeeId)
    setShowEmployeeProfile(true)
  }

  const handleEditEmployee = (employee: Employee) => {
    setEditingEmployee(employee)
    setShowEmployeeForm(true)
  }

  const handleAddEmployee = () => {
    setEditingEmployee(null)
    setShowEmployeeForm(true)
  }

  const handleDeleteEmployee = async (employeeId: string) => {
    if (confirm("Are you sure you want to delete this employee?")) {
      const response = await employeeApi.deleteEmployee(employeeId)
      if (handleApiResponse(response, "Employee deleted successfully")) {
        await loadEmployees()
      }
    }
  }

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case 'delete':
          if (confirm(`Are you sure you want to delete ${selectedEmployees.length} employees?`)) {
            // Implement bulk delete
            toast.success(`${selectedEmployees.length} employees deleted`)
          }
          break
        case 'export':
          const response = await employeeApi.exportEmployees({
            search: selectedEmployees.join(',')
          })
          if (handleApiResponse(response, "Export started")) {
            // Handle export
          }
          break
        default:
          console.log(`Performing ${action} on employees:`, selectedEmployees)
      }
    } catch (error) {
      toast.error("Bulk action failed")
    } finally {
      setSelectedEmployees([])
      setShowBulkActions(false)
    }
  }

  const toggleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("asc")
    }
  }

  const handleRefresh = async () => {
    await withLoading(loadEmployees, setIsRefreshing)
  }

  const handleSwipeLeft = (employeeId: string) => {
    handleDeleteEmployee(employeeId)
  }

  const handleSwipeRight = (employeeId: string) => {
    // Implement archive functionality
    toast.info("Archive functionality coming soon")
  }

  const handleEmployeeFormSubmit = async (data: any) => {
    try {
      if (editingEmployee) {
        const response = await employeeApi.updateEmployee(editingEmployee.id, data)
        if (handleApiResponse(response, "Employee updated successfully")) {
          setShowEmployeeForm(false)
          setEditingEmployee(null)
          await loadEmployees()
        }
      } else {
        const response = await employeeApi.createEmployee(data)
        if (handleApiResponse(response, "Employee created successfully")) {
          setShowEmployeeForm(false)
          await loadEmployees()
        }
      }
    } catch (error) {
      console.error("Form submission error:", error)
      toast.error("Failed to save employee")
    }
  }

  const handleCloseForm = () => {
    setShowEmployeeForm(false)
    setEditingEmployee(null)
  }

  const handleCloseProfile = () => {
    setShowEmployeeProfile(false)
    setSelectedEmployeeId(null)
  }

  return (
    <PullToRefresh onRefresh={handleRefresh} className="flex-1">
      <div className="space-y-6 p-6">
      <Header
        title="Employee Management"
        subtitle={`Managing ${totalEmployees} employees across ${departments.length - 1} departments`}
        actions={
          <div className="flex items-center space-x-2">
            {selectedEmployees.length > 0 && (
              <div className="flex items-center space-x-2 mr-4">
                <span className="text-sm text-muted-foreground">
                  {selectedEmployees.length} selected
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBulkActions(!showBulkActions)}
                >
                  <CheckSquare className="w-4 h-4 mr-2" />
                  Bulk Actions
                </Button>
              </div>
            )}
            <TouchButton
              variant="secondary"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </TouchButton>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button size="sm" onClick={handleAddEmployee}>
              <Plus className="w-4 h-4 mr-2" />
              Add Employee
            </Button>
          </div>
        }
      />

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedEmployees.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-900">
                Bulk Actions for {selectedEmployees.length} employees:
              </span>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('send-email')}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('export-data')}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Export Data
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('update-status')}
                >
                  <UserCheck className="w-4 h-4 mr-2" />
                  Update Status
                </Button>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowBulkActions(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </motion.div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees by name, email, position, or department..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by department"
              >
                <option key="all" value="All">All Departments</option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.name}>{dept.name} Department</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by status"
              >
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {status === "All" ? "All Status" : status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={showAdvancedFilters ? "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800" : ""}
              >
                <Filter className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => toggleSort(sortBy)}
              >
                {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    aria-label="Sort by field"
                  >
                    <option value="name">Name</option>
                    <option value="department">Department</option>
                    <option value="performance">Performance</option>
                    <option value="startDate">Start Date</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Performance Level
                  </label>
                  <select
                    value={performanceFilter}
                    onChange={(e) => setPerformanceFilter(e.target.value as typeof performanceFilter)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    aria-label="Filter by performance level"
                  >
                    <option value="all">All Performance Levels</option>
                    <option value="high">High (4.5+)</option>
                    <option value="medium">Medium (3.5-4.4)</option>
                    <option value="low">Low (&lt;3.5)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Salary Range
                  </label>
                  <div className="flex items-center space-x-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={salaryRange[0]}
                      onChange={(e) => setSalaryRange([parseInt(e.target.value) || 0, salaryRange[1]])}
                      className="w-20"
                    />
                    <span className="text-muted-foreground">-</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={salaryRange[1]}
                      onChange={(e) => setSalaryRange([salaryRange[0], parseInt(e.target.value) || 200000])}
                      className="w-20"
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{employees.length}</p>
                <p className="text-sm text-muted-foreground">Total Employees</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {employees.filter(emp => emp.status === "active").length}
                </p>
                <p className="text-sm text-muted-foreground">Active</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {employees.filter(emp => emp.status === "on-leave").length}
                </p>
                <p className="text-sm text-muted-foreground">On Leave</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {(employees.reduce((sum, emp) => sum + emp.performance, 0) / employees.length).toFixed(1)}
                </p>
                <p className="text-sm text-muted-foreground">Avg Performance</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <p className="text-sm text-muted-foreground">
            Showing {filteredAndSortedEmployees.length} of {employees.length} employees
          </p>
          {selectedEmployees.length > 0 && (
            <div className="flex items-center space-x-2">
              <CheckSquare className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-600 font-medium">
                {selectedEmployees.length} selected
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedEmployees([])}
                className="text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </Button>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid3X3 className="h-4 w-4 mr-2" />
            Grid
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4 mr-2" />
            List
          </Button>
        </div>
      </div>

      {/* Employee Grid */}
      {viewMode === "grid" && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredAndSortedEmployees.map((employee, index) => (
            <motion.div
              key={employee.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <SwipeableCard
                onSwipeLeft={() => handleSwipeLeft(employee.id)}
                onSwipeRight={() => handleSwipeRight(employee.id)}
                className={`hover:shadow-lg transition-all cursor-pointer ${
                  selectedEmployees.includes(employee.id) ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20' : ''
                }`}
              >
                <Card className="border-0 shadow-none bg-transparent">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <input
                          type="checkbox"
                          checked={selectedEmployees.includes(employee.id)}
                          onChange={() => handleSelectEmployee(employee.id)}
                          className="absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          aria-label={`Select ${employee.name}`}
                        />
                        <Avatar className="h-12 w-12 ml-6">
                          <AvatarImage src={employee.avatar} alt={employee.name} />
                          <AvatarFallback>
                            {employee.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground">{employee.name}</h3>
                        <p className="text-sm text-muted-foreground">{employee.position}</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge variant={getStatusColor(employee.status) as any}>
                      {employee.status.replace('-', ' ')}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Star className={`h-4 w-4 ${getPerformanceColor(employee.performance)}`} />
                      <span className={`text-sm font-medium ${getPerformanceColor(employee.performance)}`}>
                        {employee.performance}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4" />
                      <span className="truncate">{employee.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4" />
                      <span>{employee.department}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4" />
                      <span>{employee.location}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>Started {new Date(employee.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4" />
                      <span>${employee.salary.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
              </SwipeableCard>
            </motion.div>
          ))}
        </div>
      )}

      {/* Employee List View */}
      {viewMode === "list" && (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/50 border-b border-border">
                  <tr>
                    <th className="text-left py-3 px-6 font-medium text-foreground">
                      <input
                        type="checkbox"
                        checked={selectedEmployees.length === filteredAndSortedEmployees.length && filteredAndSortedEmployees.length > 0}
                        onChange={handleSelectAll}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        aria-label="Select all employees"
                      />
                    </th>
                    <th className="text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50" onClick={() => toggleSort("name")}>
                      <div className="flex items-center space-x-1">
                        <span>Employee</span>
                        {sortBy === "name" && (sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />)}
                      </div>
                    </th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Position</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50" onClick={() => toggleSort("department")}>
                      <div className="flex items-center space-x-1">
                        <span>Department</span>
                        {sortBy === "department" && (sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />)}
                      </div>
                    </th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Status</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50" onClick={() => toggleSort("performance")}>
                      <div className="flex items-center space-x-1">
                        <span>Performance</span>
                        {sortBy === "performance" && (sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />)}
                      </div>
                    </th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Salary</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndSortedEmployees.map((employee, index) => (
                    <motion.tr
                      key={employee.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className={`border-b border-border hover:bg-muted/50 ${
                        selectedEmployees.includes(employee.id) ? 'bg-blue-50 dark:bg-blue-950/20' : ''
                      }`}
                    >
                      <td className="py-4 px-6">
                        <input
                          type="checkbox"
                          checked={selectedEmployees.includes(employee.id)}
                          onChange={() => handleSelectEmployee(employee.id)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          aria-label={`Select ${employee.name}`}
                        />
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={employee.avatar} alt={employee.name} />
                            <AvatarFallback>
                              {employee.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-foreground">{employee.name}</p>
                            <p className="text-sm text-muted-foreground">{employee.email}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-foreground">{employee.position}</td>
                      <td className="py-4 px-6 text-muted-foreground">{employee.department}</td>
                      <td className="py-4 px-6">
                        <Badge variant={getStatusColor(employee.status) as any}>
                          {employee.status.replace('-', ' ')}
                        </Badge>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-1">
                          <Star className={`h-4 w-4 ${getPerformanceColor(employee.performance)}`} />
                          <span className={`font-medium ${getPerformanceColor(employee.performance)}`}>
                            {employee.performance}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-foreground font-medium">
                        ${employee.salary.toLocaleString()}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Floating Action Button for Mobile */}
      <FloatingActionButton
        icon={<Plus className="h-6 w-6" />}
        onClick={handleAddEmployee}
        position="bottom-right"
        className="lg:hidden"
      />

      {/* Employee Form Modal */}
      <Dialog open={showEmployeeForm} onOpenChange={setShowEmployeeForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingEmployee ? "Edit Employee" : "Add New Employee"}
            </DialogTitle>
          </DialogHeader>
          <EmployeeForm
            employee={editingEmployee}
            onSubmit={handleEmployeeFormSubmit}
            onCancel={handleCloseForm}
            departments={departments}
            positions={positions}
            managers={managers}
          />
        </DialogContent>
      </Dialog>

      {/* Employee Profile Modal */}
      <Dialog open={showEmployeeProfile} onOpenChange={setShowEmployeeProfile}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Employee Profile</DialogTitle>
          </DialogHeader>
          {selectedEmployeeId && (
            <EmployeeProfile
              employeeId={selectedEmployeeId}
              onEdit={() => {
                const employee = employees.find(e => e.id === selectedEmployeeId)
                if (employee) {
                  setShowEmployeeProfile(false)
                  handleEditEmployee(employee)
                }
              }}
              onClose={handleCloseProfile}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading employees...</span>
          </div>
        </div>
      )}
    </div>
    </PullToRefresh>
  )
}
