import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/providers/theme-provider";

const inter = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "PeopleNest - Enterprise HRMS Platform",
  description: "Modern, AI-powered Human Resource Management System for enterprise organizations",
  keywords: ["HRMS", "HR", "Human Resources", "Employee Management", "Payroll", "Performance"],
  manifest: "/manifest.json",
  themeColor: "#3b82f6",
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    viewportFit: "cover"
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PeopleNest HRMS"
  },
  formatDetection: {
    telephone: false
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "PeopleNest",
    "application-name": "PeopleNest HRMS",
    "msapplication-TileColor": "#3b82f6",
    "msapplication-config": "/browserconfig.xml"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3b82f6" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="PeopleNest" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="application-name" content="PeopleNest HRMS" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased touch-manipulation`}
      >
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `,
          }}
        />
        <ThemeProvider>
          {children}
        </ThemeProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
