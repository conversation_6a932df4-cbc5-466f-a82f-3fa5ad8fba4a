import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authMiddleware, requirePermission } from '../middleware/auth'
import { PositionService } from '../services/positionService'
import { logger } from '../utils/logger'

const router = express.Router()
const positionService = new PositionService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route GET /api/positions
 * @desc Get all positions with filtering
 * @access HR, Manager
 */
router.get('/',
  authMiddleware,
  requirePermission('hr', 'manager'),
  [
    query('departmentId').optional().isUUID(),
    query('level').optional().isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    query('type').optional().isIn(['full_time', 'part_time', 'contract', 'intern']),
    query('includeInactive').optional().isBoolean(),
    query('search').optional().isString(),
    query('sortBy').optional().isIn(['title', 'level', 'department', 'createdAt']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  validateRequest,
  async (req, res) => {
    try {
      const positions = await positionService.getAllPositions(req.query, req.user)
      res.json(positions)
    } catch (error) {
      logger.error('Error fetching positions:', error)
      res.status(500).json({ error: 'Failed to fetch positions' })
    }
  }
)

/**
 * @route GET /api/positions/:positionId
 * @desc Get position by ID with details
 * @access HR, Manager
 */
router.get('/:positionId',
  authMiddleware,
  requirePermission('hr', 'manager'),
  [param('positionId').isUUID().withMessage('Invalid position ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { positionId } = req.params
      const position = await positionService.getPositionById(positionId, req.user)
      
      if (!position) {
        return res.status(404).json({ error: 'Position not found' })
      }

      res.json(position)
    } catch (error) {
      logger.error('Error fetching position:', error)
      res.status(500).json({ error: 'Failed to fetch position' })
    }
  }
)

/**
 * @route POST /api/positions
 * @desc Create new position
 * @access HR Admin
 */
router.post('/',
  authMiddleware,
  requirePermission('hr_admin'),
  [
    body('title').notEmpty().withMessage('Position title is required'),
    body('description').optional().isString(),
    body('departmentId').isUUID().withMessage('Department ID is required'),
    body('level').isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    body('type').isIn(['full_time', 'part_time', 'contract', 'intern']),
    body('salaryMin').optional().isNumeric(),
    body('salaryMax').optional().isNumeric(),
    body('currency').optional().isString(),
    body('requiredSkills').optional().isArray(),
    body('preferredSkills').optional().isArray(),
    body('responsibilities').optional().isArray(),
    body('requirements').optional().isArray(),
    body('reportsTo').optional().isUUID(),
    body('isRemoteEligible').optional().isBoolean(),
    body('travelRequired').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const position = await positionService.createPosition(req.body, req.user.id)
      
      logger.info(`Position created: ${position.id}`, { 
        userId: req.user.id,
        positionTitle: req.body.title 
      })
      
      res.status(201).json(position)
    } catch (error) {
      logger.error('Error creating position:', error)
      if (error.message.includes('already exists')) {
        res.status(409).json({ error: error.message })
      } else {
        res.status(500).json({ error: 'Failed to create position' })
      }
    }
  }
)

/**
 * @route PUT /api/positions/:positionId
 * @desc Update position
 * @access HR Admin
 */
router.put('/:positionId',
  authMiddleware,
  requirePermission('hr_admin'),
  [
    param('positionId').isUUID().withMessage('Invalid position ID'),
    body('title').optional().notEmpty(),
    body('description').optional().isString(),
    body('departmentId').optional().isUUID(),
    body('level').optional().isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    body('type').optional().isIn(['full_time', 'part_time', 'contract', 'intern']),
    body('salaryMin').optional().isNumeric(),
    body('salaryMax').optional().isNumeric(),
    body('currency').optional().isString(),
    body('requiredSkills').optional().isArray(),
    body('preferredSkills').optional().isArray(),
    body('responsibilities').optional().isArray(),
    body('requirements').optional().isArray(),
    body('reportsTo').optional().isUUID(),
    body('isRemoteEligible').optional().isBoolean(),
    body('travelRequired').optional().isBoolean(),
    body('isActive').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { positionId } = req.params
      const position = await positionService.updatePosition(positionId, req.body, req.user.id)
      
      if (!position) {
        return res.status(404).json({ error: 'Position not found' })
      }

      logger.info(`Position updated: ${positionId}`, { userId: req.user.id })
      res.json(position)
    } catch (error) {
      logger.error('Error updating position:', error)
      if (error.message.includes('already exists')) {
        res.status(409).json({ error: error.message })
      } else {
        res.status(500).json({ error: 'Failed to update position' })
      }
    }
  }
)

/**
 * @route DELETE /api/positions/:positionId
 * @desc Deactivate position (soft delete)
 * @access HR Admin
 */
router.delete('/:positionId',
  authMiddleware,
  requirePermission('hr_admin'),
  [param('positionId').isUUID().withMessage('Invalid position ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { positionId } = req.params
      const success = await positionService.deactivatePosition(positionId, req.user.id)
      
      if (!success) {
        return res.status(404).json({ error: 'Position not found or has active employees' })
      }

      logger.info(`Position deactivated: ${positionId}`, { userId: req.user.id })
      res.json({ message: 'Position deactivated successfully' })
    } catch (error) {
      logger.error('Error deactivating position:', error)
      res.status(500).json({ error: 'Failed to deactivate position' })
    }
  }
)

/**
 * @route GET /api/positions/:positionId/employees
 * @desc Get employees in position
 * @access HR, Manager
 */
router.get('/:positionId/employees',
  authMiddleware,
  requirePermission('hr', 'manager'),
  [
    param('positionId').isUUID().withMessage('Invalid position ID'),
    query('status').optional().isIn(['active', 'inactive', 'terminated'])
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { positionId } = req.params
      const employees = await positionService.getPositionEmployees(positionId, req.query, req.user)
      res.json(employees)
    } catch (error) {
      logger.error('Error fetching position employees:', error)
      res.status(500).json({ error: 'Failed to fetch position employees' })
    }
  }
)

/**
 * @route GET /api/positions/department/:departmentId
 * @desc Get positions by department
 * @access HR, Manager
 */
router.get('/department/:departmentId',
  authMiddleware,
  requirePermission('hr', 'manager'),
  [
    param('departmentId').isUUID().withMessage('Invalid department ID'),
    query('includeInactive').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { departmentId } = req.params
      const positions = await positionService.getPositionsByDepartment(departmentId, req.query, req.user)
      res.json(positions)
    } catch (error) {
      logger.error('Error fetching department positions:', error)
      res.status(500).json({ error: 'Failed to fetch department positions' })
    }
  }
)

/**
 * @route GET /api/positions/analytics/overview
 * @desc Get position analytics overview
 * @access HR Admin
 */
router.get('/analytics/overview',
  authMiddleware,
  requirePermission('hr_admin'),
  [
    query('departmentId').optional().isUUID(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const analytics = await positionService.getPositionAnalytics(req.query, req.user)
      res.json(analytics)
    } catch (error) {
      logger.error('Error fetching position analytics:', error)
      res.status(500).json({ error: 'Failed to fetch position analytics' })
    }
  }
)

export default router
