"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { Eye, EyeOff, Mail, Lock, Building2, Shield, AlertCircle, CheckCircle2 } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/providers/auth-provider"

// Form validation schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 characters"),
  rememberMe: z.boolean().default(false)
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [loginError, setLoginError] = useState<string | null>(null)
  const [loginSuccess, setLoginSuccess] = useState(false)
  const router = useRouter()
  const { login, isAuthenticated, isLoading: authLoading } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    trigger
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "<EMAIL>",
      password: "awadhesh123",
      rememberMe: false
    },
    mode: "onChange"
  })

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      router.push("/dashboard")
    }
  }, [isAuthenticated, authLoading, router])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + D to fill demo credentials in development
      if (process.env.NODE_ENV === 'development' && event.altKey && event.key === 'd') {
        event.preventDefault()
        const emailInput = document.querySelector('input[name="email"]') as HTMLInputElement
        const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement

        if (emailInput && passwordInput) {
          emailInput.value = '<EMAIL>'
          passwordInput.value = 'awadhesh123'
          emailInput.dispatchEvent(new Event('input', { bubbles: true }))
          passwordInput.dispatchEvent(new Event('input', { bubbles: true }))
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const onSubmit = async (data: LoginFormData) => {
    try {
      setLoginError(null)
      setLoginSuccess(false)

      const success = await login(data.email, data.password)

      if (success) {
        setLoginSuccess(true)
        // Small delay to show success message before redirect
        setTimeout(() => {
          router.push("/dashboard")
        }, 1000)
      } else {
        setLoginError("Invalid email or password. Please try again.")
      }
    } catch (error) {
      console.error("Login error:", error)
      setLoginError("An unexpected error occurred. Please try again.")
    }
  }

  // Watch form values for real-time validation feedback
  const watchedEmail = watch("email")
  const watchedPassword = watch("password")

  // Show loading state during auth check
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        {/* Logo and Branding */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg"
          >
            <Building2 className="w-8 h-8 text-white" />
          </motion.div>
          <h1 className="text-3xl font-bold text-foreground mb-2">PeopleNest</h1>
          <p className="text-muted-foreground">Enterprise HRMS Platform</p>
          <div className="flex items-center justify-center gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              <Shield className="w-3 h-3 mr-1" />
              SOC 2 Compliant
            </Badge>
            <Badge variant="outline" className="text-xs">
              Enterprise Ready
            </Badge>
          </div>
        </div>

        {/* Login Form */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-2xl font-semibold text-center">
              Welcome back
            </CardTitle>
            <CardDescription className="text-center">
              Sign in to your account to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Success Message */}
            {loginSuccess && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4"
              >
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    Login successful! Redirecting to dashboard...
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}

            {/* Error Message */}
            {loginError && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4"
              >
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{loginError}</AlertDescription>
                </Alert>
              </motion.div>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Input
                    {...register("email")}
                    type="email"
                    placeholder="Enter your email"
                    label="Email Address"
                    leftIcon={<Mail className="w-4 h-4" />}
                    error={errors.email?.message}
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Input
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    label="Password"
                    leftIcon={<Lock className="w-4 h-4" />}
                    rightIcon={
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-muted-foreground hover:text-foreground transition-colors"
                        disabled={isSubmitting}
                      >
                        {showPassword ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </button>
                    }
                    error={errors.password?.message}
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center space-x-2 text-sm">
                  <input
                    {...register("rememberMe")}
                    type="checkbox"
                    className="rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50"
                    disabled={isSubmitting}
                  />
                  <span className="text-muted-foreground">Remember me</span>
                </label>
                <a
                  href="#"
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed"
                  onClick={(e) => e.preventDefault()}
                >
                  Forgot password? (Coming Soon)
                </a>
              </div>

              <Button
                type="submit"
                className="w-full h-11 text-base font-medium"
                loading={isSubmitting}
                disabled={isSubmitting || loginSuccess}
              >
                {isSubmitting ? "Signing in..." : loginSuccess ? "Success!" : "Sign in"}
              </Button>
            </form>

            {/* Demo Credentials for Development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-blue-900">Demo Credentials</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => {
                      const form = document.querySelector('form') as HTMLFormElement
                      const emailInput = form?.querySelector('input[name="email"]') as HTMLInputElement
                      const passwordInput = form?.querySelector('input[name="password"]') as HTMLInputElement

                      if (emailInput && passwordInput) {
                        emailInput.value = '<EMAIL>'
                        passwordInput.value = 'awadhesh123'
                        emailInput.dispatchEvent(new Event('input', { bubbles: true }))
                        passwordInput.dispatchEvent(new Event('input', { bubbles: true }))
                      }
                    }}
                    disabled={isSubmitting}
                  >
                    Quick Fill
                  </Button>
                </div>
                <div className="space-y-1 text-xs text-blue-800">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Password:</strong> awadhesh123</p>
                  <p className="text-blue-600 mt-2">Super Admin credentials - pre-filled for development</p>
                  <p className="text-blue-500 mt-1">Tip: Press Alt + D to quick fill</p>
                </div>
              </div>
            )}

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Don&apos;t have an account?{" "}
                <a
                  href="#"
                  className="text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed"
                  onClick={(e) => e.preventDefault()}
                >
                  Contact your administrator
                </a>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.3 }}
          className="mt-6 text-center"
        >
          <p className="text-xs text-muted-foreground">
            Protected by enterprise-grade security and encryption
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}
