const axios = require('axios');

async function testRateLimitFix() {
  console.log('🔧 Testing Rate Limit Fix for awadhesh Login');
  console.log('=' .repeat(50));
  
  try {
    // Test multiple rapid login attempts to verify rate limiting is relaxed
    console.log('\n1️⃣ Testing Multiple Rapid Login Attempts...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'awadhesh123'
    };
    
    for (let i = 1; i <= 5; i++) {
      console.log(`   Attempt ${i}...`);
      
      try {
        const response = await axios.post('http://localhost:3002/api/auth/login', loginData, {
          headers: { 'Content-Type': 'application/json' }
        });
        
        console.log(`   ✅ Attempt ${i} successful - Status: ${response.status}`);
        
        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        if (error.response?.status === 429) {
          console.log(`   ❌ Attempt ${i} rate limited - Status: 429`);
        } else {
          console.log(`   ❌ Attempt ${i} failed - Status: ${error.response?.status || 'Unknown'}`);
          console.log(`   Error: ${error.response?.data?.message || error.message}`);
        }
      }
    }
    
    // Test successful login with token verification
    console.log('\n2️⃣ Testing Full Login Flow...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', loginData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Login successful!');
    console.log('   User:', loginResponse.data.user.email);
    console.log('   Role:', loginResponse.data.user.role);
    console.log('   Permissions:', loginResponse.data.user.permissions.length);
    
    // Test protected endpoint access
    console.log('\n3️⃣ Testing Protected Endpoint Access...');
    const token = loginResponse.data.tokens.accessToken;
    
    const employeesResponse = await axios.get('http://localhost:3002/api/employees', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json' 
      }
    });
    
    console.log('✅ Protected endpoint accessible');
    console.log('   Status:', employeesResponse.status);
    
    console.log('\n🎉 RATE LIMIT FIX SUMMARY');
    console.log('=' .repeat(40));
    console.log('✅ Rate limiting configuration updated');
    console.log('✅ Multiple login attempts now allowed');
    console.log('✅ awadhesh user authentication working');
    console.log('✅ Protected endpoints accessible');
    
    console.log('\n📋 UPDATED RATE LIMITS:');
    console.log('   Max requests: 1000 per window');
    console.log('   Window duration: 10 seconds');
    console.log('   This should prevent rate limiting during development');
    
    console.log('\n🌐 FRONTEND ACCESS:');
    console.log('   URL: http://localhost:3005');
    console.log('   Default credentials: <EMAIL> / awadhesh123');
    console.log('   You can now log in through the frontend without rate limiting issues!');
    
  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    } else {
      console.error('   Error:', error.message);
    }
  }
}

testRateLimitFix();
