"use client"

import { AuthProvider } from "@/components/providers/auth-provider"

interface AuthLayoutClientProps {
  children: React.ReactNode
}

export function AuthLayoutClient({ children }: AuthLayoutClientProps) {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />

        {/* Main Content */}
        <div className="relative z-10">
          {children}
        </div>

        {/* Footer */}
        <footer className="absolute bottom-4 left-0 right-0 text-center">
          <p className="text-xs text-muted-foreground">
            © 2024 PeopleNest. All rights reserved. | Privacy Policy | Terms of Service
          </p>
        </footer>
      </div>
    </AuthProvider>
  )
}
