import express from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { body, validationResult } from 'express-validator'
import { DatabaseService } from '../services/databaseService'
import { logger, loggers } from '../utils/logger'
import { jwtService, JWTService } from '../services/jwtService'
import { auditLogger } from '../utils/auditLogger'
import crypto from 'crypto'

const router = express.Router()
const db = new DatabaseService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route POST /api/auth/login
 * @desc Authenticate user and return JWT token
 * @access Public
 */
router.post('/login',
  [
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').notEmpty().withMessage('Password is required')
  ],
  validateRequest,
  async (req: express.Request, res: express.Response) => {
    try {
      const { email, password } = req.body
      const clientIp = req.ip || req.connection.remoteAddress

      // Find user by email
      const userResult = await db.query(`
        SELECT
          u.id,
          u.email,
          u.password_hash,
          u.role,
          u.is_active,
          u.failed_login_attempts,
          u.locked_until,
          e.id as employee_id,
          pgp_sym_decrypt(e.first_name_encrypted, $2) as first_name,
          pgp_sym_decrypt(e.last_name_encrypted, $2) as last_name,
          e.status as employee_status
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        WHERE u.email = $1
      `, [email.toLowerCase(), process.env.ENCRYPTION_KEY || 'default-key'])

      if (userResult.rows.length === 0) {
        loggers.auth.loginFailed(email, clientIp, 'User not found')
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid email or password'
        })
      }

      const user = userResult.rows[0]

      // Check if account is locked
      if (user.locked_until && new Date(user.locked_until) > new Date()) {
        loggers.auth.loginFailed(email, clientIp, 'Account locked')
        return res.status(423).json({
          error: 'Account Locked',
          message: 'Account is temporarily locked due to multiple failed login attempts'
        })
      }

      // Check if user is active
      if (!user.is_active) {
        loggers.auth.loginFailed(email, clientIp, 'Account inactive')
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Account is not active'
        })
      }

      // Check if employee is active (if user is linked to an employee)
      if (user.employee_id && user.employee_status !== 'active') {
        loggers.auth.loginFailed(email, clientIp, 'Employee inactive')
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Employee account is not active'
        })
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password_hash)
      
      if (!isPasswordValid) {
        // Increment failed login attempts
        const failedAttempts = (user.failed_login_attempts || 0) + 1
        let lockUntil = null

        // Lock account after 5 failed attempts for 30 minutes
        if (failedAttempts >= 5) {
          lockUntil = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
        }

        await db.query(`
          UPDATE users 
          SET failed_login_attempts = $1, locked_until = $2, updated_at = NOW()
          WHERE id = $3
        `, [failedAttempts, lockUntil, user.id])

        loggers.auth.loginFailed(email, clientIp, 'Invalid password')
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid email or password'
        })
      }

      // Reset failed login attempts on successful login
      await db.query(`
        UPDATE users 
        SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW(), updated_at = NOW()
        WHERE id = $1
      `, [user.id])

      // Generate device fingerprint
      const userAgent = req.get('User-Agent') || ''
      const acceptLanguage = req.get('Accept-Language') || ''
      const acceptEncoding = req.get('Accept-Encoding') || ''
      const deviceFingerprint = JWTService.generateDeviceFingerprint(userAgent, acceptLanguage, acceptEncoding)

      // Generate session ID
      const sessionId = crypto.randomUUID()

      // Get user permissions
      const permissions = await getUserPermissions(user.role)

      // Generate enhanced token pair using JWT service
      const tokenPair = await jwtService.generateTokenPair(
        user.id,
        user.email,
        user.role,
        permissions,
        sessionId,
        deviceFingerprint,
        clientIp,
        userAgent
      )

      // Log successful authentication
      loggers.auth.login(user.id, user.email, clientIp)

      // Log audit event
      await auditLogger.logAuthEvent(
        'LOGIN',
        user.id,
        user.email,
        req,
        {
          sessionId,
          deviceFingerprint: deviceFingerprint.substring(0, 8) + '...',
          role: user.role
        }
      )

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          employeeId: user.employee_id,
          permissions
        },
        tokens: {
          accessToken: tokenPair.accessToken,
          refreshToken: tokenPair.refreshToken,
          expiresIn: tokenPair.expiresIn,
          tokenType: tokenPair.tokenType
        },
        sessionId,
        deviceFingerprint
      })
    } catch (error) {
      logger.error('Login error:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Login failed'
      })
    }
  }
)

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token using refresh token
 * @access Public
 */
router.post('/refresh',
  [
    body('refreshToken').notEmpty().withMessage('Refresh token is required'),
    body('deviceFingerprint').optional().isString().withMessage('Device fingerprint must be a string')
  ],
  validateRequest,
  async (req: express.Request, res: express.Response) => {
    try {
      const { refreshToken, deviceFingerprint } = req.body

      // Generate device fingerprint if not provided
      let currentDeviceFingerprint = deviceFingerprint
      if (!currentDeviceFingerprint) {
        const userAgent = req.get('User-Agent') || ''
        const acceptLanguage = req.get('Accept-Language') || ''
        const acceptEncoding = req.get('Accept-Encoding') || ''
        currentDeviceFingerprint = JWTService.generateDeviceFingerprint(userAgent, acceptLanguage, acceptEncoding)
      }

      // Use enhanced JWT service to refresh token
      const newTokenPair = await jwtService.refreshAccessToken(refreshToken, currentDeviceFingerprint)

      logger.info('Token refreshed successfully', {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      res.json({
        message: 'Token refreshed successfully',
        tokens: {
          accessToken: newTokenPair.accessToken,
          refreshToken: newTokenPair.refreshToken,
          expiresIn: newTokenPair.expiresIn,
          tokenType: newTokenPair.tokenType
        }
      })
    } catch (error) {
      logger.warn('Token refresh failed:', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired refresh token',
        code: 'REFRESH_FAILED'
      })
    }
  }
)

/**
 * @route GET /api/auth/me
 * @desc Get current user profile information
 * @access Private
 */
router.get('/me',
  async (req: express.Request, res: express.Response) => {
    try {
      const authHeader = req.headers.authorization

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Access token required',
          code: 'TOKEN_REQUIRED'
        })
      }

      const token = authHeader.substring(7)

      // Verify token using enhanced JWT service
      const tokenPayload = await jwtService.verifyAccessToken(token)

      // Get user details from database with encrypted employee data
      const query = `
        SELECT
          u.id,
          u.email,
          u.role,
          u.employee_id,
          u.is_active,
          u.created_at,
          u.last_login,
          e.id as employee_id,
          pgp_sym_decrypt(e.first_name_encrypted, $2) as first_name,
          pgp_sym_decrypt(e.last_name_encrypted, $2) as last_name,
          e.status as employee_status
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        WHERE u.id = $1 AND u.is_active = true
      `

      const result = await db.query(query, [tokenPayload.userId, process.env.ENCRYPTION_KEY || 'default-key'])

      if (result.rows.length === 0) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User account not found or inactive',
          code: 'USER_NOT_FOUND'
        })
      }

      const user = result.rows[0]

      res.json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          employeeId: user.employee_id,
          isActive: user.is_active,
          createdAt: user.created_at,
          lastLogin: user.last_login,
          permissions: tokenPayload.permissions
        },
        sessionId: tokenPayload.sessionId
      })
    } catch (error) {
      logger.warn('Get user profile failed:', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      if (error.message.includes('expired') || error.message.includes('invalid')) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid or expired access token',
          code: 'TOKEN_INVALID'
        })
      }

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get user profile',
        code: 'PROFILE_FETCH_FAILED'
      })
    }
  }
)

/**
 * @route POST /api/auth/logout
 * @desc Logout user and invalidate refresh token
 * @access Private
 */
router.post('/logout',
  [
    body('refreshToken').optional().isString(),
    body('sessionId').optional().isString()
  ],
  async (req: express.Request, res: express.Response) => {
    try {
      const { refreshToken, sessionId } = req.body
      const authHeader = req.headers.authorization
      let userId: string | null = null
      let userEmail: string | null = null

      // Handle access token logout
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        try {
          // Verify token using enhanced JWT service
          const tokenPayload = await jwtService.verifyAccessToken(token)
          userId = tokenPayload.userId
          userEmail = tokenPayload.email

          // Revoke access token
          await jwtService.revokeAccessToken(token)

          // Revoke all tokens for the session if sessionId provided
          if (sessionId || tokenPayload.sessionId) {
            await jwtService.revokeAllTokens(sessionId || tokenPayload.sessionId)
          }

          // Log audit event
          await auditLogger.logAuthEvent(
            'LOGOUT',
            userId,
            userEmail,
            req,
            { sessionId: sessionId || tokenPayload.sessionId }
          )

          loggers.auth.logout(userId, userEmail)
        } catch (error) {
          // Token might be invalid, but we still want to logout
          logger.warn('Invalid token during logout:', error.message)
        }
      }

      // Handle refresh token logout
      if (refreshToken) {
        try {
          // Extract token ID from refresh token and revoke it
          const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET!) as any
          if (decoded.tokenId) {
            await jwtService.revokeRefreshToken(decoded.tokenId)
          }
        } catch (error) {
          logger.warn('Invalid refresh token during logout:', error.message)
        }
      }

      res.json({
        message: 'Logout successful',
        code: 'LOGOUT_SUCCESS'
      })
    } catch (error) {
      logger.error('Logout error:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Logout failed',
        code: 'LOGOUT_ERROR'
      })
    }
  }
)

/**
 * @route POST /api/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post('/forgot-password',
  [
    body('email').isEmail().withMessage('Valid email is required')
  ],
  validateRequest,
  async (req: express.Request, res: express.Response) => {
    try {
      const { email } = req.body

      // Check if user exists
      const userResult = await db.query(`
        SELECT id, email, first_name, last_name FROM users WHERE email = $1 AND is_active = true
      `, [email.toLowerCase()])

      // Always return success to prevent email enumeration
      if (userResult.rows.length === 0) {
        return res.json({
          message: 'If an account with that email exists, a password reset link has been sent.'
        })
      }

      const user = userResult.rows[0]

      // Generate reset token
      const resetToken = jwt.sign(
        { userId: user.id, type: 'password_reset' },
        process.env.JWT_SECRET!,
        { expiresIn: '1h' }
      )

      // Store reset token in database
      await db.query(`
        INSERT INTO password_reset_tokens (user_id, token, expires_at)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id) 
        DO UPDATE SET token = $2, expires_at = $3, created_at = NOW()
      `, [user.id, resetToken, new Date(Date.now() + 60 * 60 * 1000)]) // 1 hour

      // TODO: Send password reset email
      logger.info('Password reset requested', { userId: user.id, email: user.email })

      res.json({
        message: 'If an account with that email exists, a password reset link has been sent.'
      })
    } catch (error) {
      logger.error('Forgot password error:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Password reset request failed'
      })
    }
  }
)

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password using reset token
 * @access Public
 */
router.post('/reset-password',
  [
    body('token').notEmpty().withMessage('Reset token is required'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
  ],
  validateRequest,
  async (req: express.Request, res: express.Response) => {
    try {
      const { token, password } = req.body

      // Verify reset token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

      if (decoded.type !== 'password_reset') {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid reset token'
        })
      }

      // Check if token exists in database and is not expired
      const tokenResult = await db.query(`
        SELECT user_id FROM password_reset_tokens 
        WHERE user_id = $1 AND token = $2 AND expires_at > NOW()
      `, [decoded.userId, token])

      if (tokenResult.rows.length === 0) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid or expired reset token'
        })
      }

      // Hash new password
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12')
      const passwordHash = await bcrypt.hash(password, saltRounds)

      // Update password and remove reset token
      await db.transaction([
        {
          text: 'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
          params: [passwordHash, decoded.userId]
        },
        {
          text: 'DELETE FROM password_reset_tokens WHERE user_id = $1',
          params: [decoded.userId]
        },
        {
          text: 'DELETE FROM user_sessions WHERE user_id = $1',
          params: [decoded.userId]
        }
      ])

      logger.info('Password reset completed', { userId: decoded.userId })

      res.json({
        message: 'Password reset successful'
      })
    } catch (error) {
      logger.error('Reset password error:', error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Password reset failed'
      })
    }
  }
)

/**
 * Get user permissions based on role
 */
async function getUserPermissions(role: string): Promise<string[]> {
  const rolePermissions: Record<string, string[]> = {
    'super_admin': [
      'super_admin', 'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
    ],
    'hr_admin': [
      'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
    ],
    'hr': [
      'hr', 'employee', 'recruiter'
    ],
    'manager': [
      'manager', 'employee'
    ],
    'employee': [
      'employee'
    ]
  }

  return rolePermissions[role] || ['employee']
}

export default router
