"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const employeeService_1 = require("../services/employeeService");
const auth_1 = require("../middleware/auth");
const audit_1 = require("../middleware/audit");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
const employeeService = new employeeService_1.EmployeeService();
router.use(auth_1.authMiddleware);
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation Error',
            details: errors.array()
        });
    }
    next();
};
router.get('/', (0, auth_1.requirePermission)('super_admin', 'hr_admin', 'hr', 'manager'), [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('search').optional().isString(),
    (0, express_validator_1.query)('department').optional().isUUID(),
    (0, express_validator_1.query)('status').optional().isIn(['active', 'inactive', 'terminated']),
    (0, express_validator_1.query)('role').optional().isString(),
    (0, express_validator_1.query)('manager').optional().isUUID(),
    (0, express_validator_1.query)('sortBy').optional().isIn(['firstName', 'lastName', 'email', 'hireDate', 'department']),
    (0, express_validator_1.query)('sortOrder').optional().isIn(['asc', 'desc'])
], validateRequest, (0, audit_1.auditMiddleware)('employee_list', 'data_access'), async (req, res) => {
    try {
        const employees = await employeeService.getEmployees(req.query, req.user);
        res.json(employees);
    }
    catch (error) {
        logger_1.logger.error('Error fetching employees:', error);
        res.status(500).json({ error: 'Failed to fetch employees' });
    }
});
router.get('/:employeeId', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [(0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID')], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const employee = await employeeService.getEmployeeById(employeeId, req.user);
        if (!employee) {
            return res.status(404).json({ error: 'Employee not found' });
        }
        res.json(employee);
    }
    catch (error) {
        logger_1.logger.error('Error fetching employee:', error);
        res.status(500).json({ error: 'Failed to fetch employee' });
    }
});
router.post('/', (0, auth_1.requirePermission)('hr_admin'), [
    (0, express_validator_1.body)('firstName').notEmpty().withMessage('First name is required'),
    (0, express_validator_1.body)('lastName').notEmpty().withMessage('Last name is required'),
    (0, express_validator_1.body)('email').isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('personalEmail').optional().isEmail(),
    (0, express_validator_1.body)('phone').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('dateOfBirth').optional().isISO8601(),
    (0, express_validator_1.body)('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']),
    (0, express_validator_1.body)('address').optional().isObject(),
    (0, express_validator_1.body)('emergencyContact').optional().isObject(),
    (0, express_validator_1.body)('departmentId').isUUID().withMessage('Department ID is required'),
    (0, express_validator_1.body)('positionId').isUUID().withMessage('Position ID is required'),
    (0, express_validator_1.body)('managerId').optional().isUUID(),
    (0, express_validator_1.body)('employeeType').isIn(['full_time', 'part_time', 'contract', 'intern']),
    (0, express_validator_1.body)('hireDate').isISO8601().withMessage('Valid hire date is required'),
    (0, express_validator_1.body)('salary').optional().isNumeric(),
    (0, express_validator_1.body)('currency').optional().isString(),
    (0, express_validator_1.body)('workLocation').optional().isIn(['office', 'remote', 'hybrid']),
    (0, express_validator_1.body)('skills').optional().isArray(),
    (0, express_validator_1.body)('certifications').optional().isArray()
], validateRequest, async (req, res) => {
    try {
        const employee = await employeeService.createEmployee(req.body, req.user.id);
        logger_1.logger.info(`Employee created: ${employee.id}`, {
            userId: req.user.id,
            employeeEmail: req.body.email
        });
        res.status(201).json(employee);
    }
    catch (error) {
        logger_1.logger.error('Error creating employee:', error);
        res.status(500).json({ error: 'Failed to create employee' });
    }
});
router.put('/:employeeId', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [
    (0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID'),
    (0, express_validator_1.body)('firstName').optional().notEmpty(),
    (0, express_validator_1.body)('lastName').optional().notEmpty(),
    (0, express_validator_1.body)('email').optional().isEmail(),
    (0, express_validator_1.body)('personalEmail').optional().isEmail(),
    (0, express_validator_1.body)('phone').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('address').optional().isObject(),
    (0, express_validator_1.body)('emergencyContact').optional().isObject(),
    (0, express_validator_1.body)('departmentId').optional().isUUID(),
    (0, express_validator_1.body)('positionId').optional().isUUID(),
    (0, express_validator_1.body)('managerId').optional().isUUID(),
    (0, express_validator_1.body)('salary').optional().isNumeric(),
    (0, express_validator_1.body)('workLocation').optional().isIn(['office', 'remote', 'hybrid']),
    (0, express_validator_1.body)('skills').optional().isArray(),
    (0, express_validator_1.body)('certifications').optional().isArray()
], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const employee = await employeeService.updateEmployee(employeeId, req.body, req.user);
        if (!employee) {
            return res.status(404).json({ error: 'Employee not found or access denied' });
        }
        logger_1.logger.info(`Employee updated: ${employeeId}`, { userId: req.user.id });
        res.json(employee);
    }
    catch (error) {
        logger_1.logger.error('Error updating employee:', error);
        res.status(500).json({ error: 'Failed to update employee' });
    }
});
router.delete('/:employeeId', (0, auth_1.requirePermission)('hr_admin'), [(0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID')], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const success = await employeeService.deactivateEmployee(employeeId, req.user.id);
        if (!success) {
            return res.status(404).json({ error: 'Employee not found' });
        }
        logger_1.logger.info(`Employee deactivated: ${employeeId}`, { userId: req.user.id });
        res.json({ message: 'Employee deactivated successfully' });
    }
    catch (error) {
        logger_1.logger.error('Error deactivating employee:', error);
        res.status(500).json({ error: 'Failed to deactivate employee' });
    }
});
router.get('/:employeeId/profile', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [(0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID')], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const profile = await employeeService.getEmployeeProfile(employeeId, req.user);
        if (!profile) {
            return res.status(404).json({ error: 'Employee profile not found' });
        }
        res.json(profile);
    }
    catch (error) {
        logger_1.logger.error('Error fetching employee profile:', error);
        res.status(500).json({ error: 'Failed to fetch employee profile' });
    }
});
router.get('/:employeeId/team', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [(0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID')], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const team = await employeeService.getEmployeeTeam(employeeId, req.user);
        res.json(team);
    }
    catch (error) {
        logger_1.logger.error('Error fetching employee team:', error);
        res.status(500).json({ error: 'Failed to fetch employee team' });
    }
});
router.get('/:employeeId/reports', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [(0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID')], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const reports = await employeeService.getDirectReports(employeeId, req.user);
        res.json(reports);
    }
    catch (error) {
        logger_1.logger.error('Error fetching direct reports:', error);
        res.status(500).json({ error: 'Failed to fetch direct reports' });
    }
});
router.post('/:employeeId/skills', (0, auth_1.requireSelfOrManagerAccess)('employeeId'), [
    (0, express_validator_1.param)('employeeId').isUUID().withMessage('Invalid employee ID'),
    (0, express_validator_1.body)('skills').isArray().withMessage('Skills must be an array'),
    (0, express_validator_1.body)('skills.*.name').notEmpty().withMessage('Skill name is required'),
    (0, express_validator_1.body)('skills.*.level').isIn(['beginner', 'intermediate', 'advanced', 'expert']),
    (0, express_validator_1.body)('skills.*.verified').optional().isBoolean()
], validateRequest, async (req, res) => {
    try {
        const { employeeId } = req.params;
        const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user);
        logger_1.logger.info(`Skills added to employee: ${employeeId}`, {
            userId: req.user.id,
            skillsCount: req.body.skills.length
        });
        res.json(skills);
    }
    catch (error) {
        logger_1.logger.error('Error adding employee skills:', error);
        res.status(500).json({ error: 'Failed to add employee skills' });
    }
});
router.get('/analytics/overview', (0, auth_1.requirePermission)('hr_admin', 'manager'), [
    (0, express_validator_1.query)('department').optional().isUUID(),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], validateRequest, async (req, res) => {
    try {
        const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user);
        res.json(analytics);
    }
    catch (error) {
        logger_1.logger.error('Error fetching employee analytics:', error);
        res.status(500).json({ error: 'Failed to fetch employee analytics' });
    }
});
router.get('/search', (0, auth_1.requirePermission)('hr', 'manager'), [
    (0, express_validator_1.query)('q').notEmpty().withMessage('Search query is required'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 50 }),
    (0, express_validator_1.query)('includeInactive').optional().isBoolean()
], validateRequest, async (req, res) => {
    try {
        const results = await employeeService.searchEmployees(req.query, req.user);
        res.json(results);
    }
    catch (error) {
        logger_1.logger.error('Error searching employees:', error);
        res.status(500).json({ error: 'Failed to search employees' });
    }
});
exports.default = router;
//# sourceMappingURL=employees.js.map