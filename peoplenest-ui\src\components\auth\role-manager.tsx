"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAuth } from '@/components/providers/auth-provider'
import { formatRole, getRoleBadgeProps, isAdminRole } from '@/lib/roleUtils'
import { UserRole } from '@/lib/auth'
import { Shield, Crown, Users, User, Settings, Briefcase, DollarSign, Monitor } from 'lucide-react'

interface RoleManagerProps {
  isOpen: boolean
  onClose: () => void
}

const roleOptions: { value: UserRole; label: string; description: string; icon: React.ReactNode }[] = [
  {
    value: 'super_admin',
    label: 'Super Admin',
    description: 'Full system access with all permissions',
    icon: <Crown className="h-4 w-4" />
  },
  {
    value: 'system_admin',
    label: 'System Admin',
    description: 'System administration and configuration',
    icon: <Settings className="h-4 w-4" />
  },
  {
    value: 'hr_admin',
    label: 'HR Admin',
    description: 'HR management and employee administration',
    icon: <Shield className="h-4 w-4" />
  },
  {
    value: 'hr',
    label: 'HR',
    description: 'Human resources operations',
    icon: <Users className="h-4 w-4" />
  },
  {
    value: 'manager',
    label: 'Manager',
    description: 'Team and department management',
    icon: <Briefcase className="h-4 w-4" />
  },
  {
    value: 'employee',
    label: 'Employee',
    description: 'Standard employee access',
    icon: <User className="h-4 w-4" />
  }
]

export function RoleManager({ isOpen, onClose }: RoleManagerProps) {
  const { user, updateUser } = useAuth()
  const [selectedRole, setSelectedRole] = useState<UserRole>(user?.role || 'employee')
  const [isChanging, setIsChanging] = useState(false)

  const handleRoleChange = async () => {
    if (!user || selectedRole === user.role) {
      onClose()
      return
    }

    setIsChanging(true)
    
    try {
      // In a real application, you would make an API call to change the role
      // For development purposes, we'll update the user object directly
      const updatedUser = {
        ...user,
        role: selectedRole
      }
      
      updateUser(updatedUser)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onClose()
    } catch (error) {
      console.error('Failed to change role:', error)
    } finally {
      setIsChanging(false)
    }
  }

  const currentRoleBadge = getRoleBadgeProps(user?.role)
  const selectedRoleBadge = getRoleBadgeProps(selectedRole)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Role Management</span>
          </DialogTitle>
          <DialogDescription>
            Change your current role for testing different permission levels
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Role */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Current Role</label>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="outline" 
                className={`px-3 py-1 ${currentRoleBadge.className}`}
              >
                {currentRoleBadge.text}
              </Badge>
              {isAdminRole(user?.role) && (
                <Badge variant="secondary" className="text-xs">
                  Admin Level
                </Badge>
              )}
            </div>
          </div>

          {/* Role Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Select New Role</label>
            <Select value={selectedRole} onValueChange={(value: UserRole) => setSelectedRole(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((role) => (
                  <SelectItem key={role.value} value={role.value}>
                    <div className="flex items-center space-x-2">
                      {role.icon}
                      <div>
                        <div className="font-medium">{role.label}</div>
                        <div className="text-xs text-muted-foreground">{role.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Preview */}
          {selectedRole !== user?.role && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-3 bg-muted rounded-lg"
            >
              <div className="text-sm font-medium mb-2">Preview:</div>
              <div className="flex items-center space-x-2">
                <span className="text-sm">You will appear as:</span>
                <Badge 
                  variant="outline" 
                  className={`px-2 py-1 ${selectedRoleBadge.className}`}
                >
                  {selectedRoleBadge.text}
                </Badge>
              </div>
            </motion.div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isChanging}>
            Cancel
          </Button>
          <Button 
            onClick={handleRoleChange} 
            disabled={isChanging || selectedRole === user?.role}
          >
            {isChanging ? 'Changing...' : 'Change Role'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Role Badge Component for use in other parts of the app
interface RoleBadgeProps {
  role: UserRole | string | null | undefined
  showAdminIndicator?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function RoleBadge({ role, showAdminIndicator = false, size = 'md' }: RoleBadgeProps) {
  const badgeProps = getRoleBadgeProps(role)
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  }

  return (
    <div className="flex items-center space-x-2">
      <Badge 
        variant="outline" 
        className={`${sizeClasses[size]} ${badgeProps.className}`}
      >
        {badgeProps.text}
      </Badge>
      {showAdminIndicator && isAdminRole(role) && (
        <Badge variant="secondary" className="text-xs">
          Admin
        </Badge>
      )}
    </div>
  )
}
