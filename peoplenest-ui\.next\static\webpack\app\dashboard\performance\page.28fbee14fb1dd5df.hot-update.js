"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/performance/page",{

/***/ "(app-pages-browser)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(app-pages-browser)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Provide a fallback when used outside of ThemeProvider\n        return {\n            theme: 'system',\n            setTheme: ()=>{},\n            toggleTheme: ()=>{},\n            systemTheme: 'light',\n            actualTheme: 'light'\n        };\n    }\n    return context;\n}\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = 'light' } = param;\n    _s1();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [systemTheme, setSystemTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get the actual theme being applied\n    const actualTheme = theme === 'system' ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Initialize theme on mount\n            const storedTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)();\n            setThemeState(storedTheme);\n            // Detect system theme\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n            // Set mounted first to prevent hydration mismatch\n            setMounted(true);\n            // Note: Theme is already applied by the script in root layout\n            // We don't need to apply it again here to prevent hydration issues\n            // Listen for system theme changes\n            const handleSystemThemeChange = {\n                \"ThemeProvider.useEffect.handleSystemThemeChange\": (e)=>{\n                    setSystemTheme(e.matches ? 'dark' : 'light');\n                    // If using system theme, reapply it\n                    if ((0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)() === 'system') {\n                        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)('system');\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleSystemThemeChange\"];\n            mediaQuery.addEventListener('change', handleSystemThemeChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleSystemThemeChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const handleSetTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.setThemeMode)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n    };\n    const handleToggleTheme = ()=>{\n        const currentActualTheme = theme === 'system' ? systemTheme : theme;\n        const newTheme = currentActualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    // Note: We don't prevent rendering to avoid hydration mismatch\n    // The theme is already applied by the script in root layout\n    const value = {\n        theme,\n        setTheme: handleSetTheme,\n        toggleTheme: handleToggleTheme,\n        systemTheme,\n        actualTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s1(ThemeProvider, \"aR/n1RSeJUS4S0gjtRg+W2knBo8=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/theme-provider.tsx\n"));

/***/ })

});