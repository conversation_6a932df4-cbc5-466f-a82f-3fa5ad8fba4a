/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/layout.tsx */ \"(rsc)/./src/app/auth/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(rsc)/./src/components/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/auth-layout-client.tsx */ \"(rsc)/./src/app/auth/auth-layout-client.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNhdXRoLWxheW91dC1jbGllbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aExheW91dENsaWVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoTGF5b3V0Q2xpZW50XCJdICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxhdXRoLWxheW91dC1jbGllbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxQZW9wbGVOZXN0XFxwZW9wbGVuZXN0LXVpXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/auth-layout-client.tsx":
/*!*********************************************!*\
  !*** ./src/app/auth/auth-layout-client.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthLayoutClient: () => (/* binding */ AuthLayoutClient)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthLayoutClient = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthLayoutClient() from the server but AuthLayoutClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\auth-layout-client.tsx",
"AuthLayoutClient",
);

/***/ }),

/***/ "(rsc)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_layout_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth-layout-client */ \"(rsc)/./src/app/auth/auth-layout-client.tsx\");\n\n\nconst metadata = {\n    title: \"Authentication - PeopleNest HRMS\",\n    description: \"Sign in to your PeopleNest HRMS account\",\n    robots: {\n        index: false,\n        follow: false\n    }\n};\nfunction AuthLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_layout_client__WEBPACK_IMPORTED_MODULE_1__.AuthLayoutClient, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2F1dGgvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUQ7QUFFaEQsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtJQUNWO0FBQ0YsRUFBQztBQU1jLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFtQjtJQUM5RCxxQkFBTyw4REFBQ1IsaUVBQWdCQTtrQkFBRVE7Ozs7OztBQUM1QiIsInNvdXJjZXMiOlsiQzpcXFBlb3BsZU5lc3RcXHBlb3BsZW5lc3QtdWlcXHNyY1xcYXBwXFxhdXRoXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5pbXBvcnQgeyBBdXRoTGF5b3V0Q2xpZW50IH0gZnJvbSBcIi4vYXV0aC1sYXlvdXQtY2xpZW50XCJcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQXV0aGVudGljYXRpb24gLSBQZW9wbGVOZXN0IEhSTVNcIixcbiAgZGVzY3JpcHRpb246IFwiU2lnbiBpbiB0byB5b3VyIFBlb3BsZU5lc3QgSFJNUyBhY2NvdW50XCIsXG4gIHJvYm90czoge1xuICAgIGluZGV4OiBmYWxzZSxcbiAgICBmb2xsb3c6IGZhbHNlLFxuICB9LFxufVxuXG5pbnRlcmZhY2UgQXV0aExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoTGF5b3V0KHsgY2hpbGRyZW4gfTogQXV0aExheW91dFByb3BzKSB7XG4gIHJldHVybiA8QXV0aExheW91dENsaWVudD57Y2hpbGRyZW59PC9BdXRoTGF5b3V0Q2xpZW50PlxufVxuIl0sIm5hbWVzIjpbIkF1dGhMYXlvdXRDbGllbnQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsIkF1dGhMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c31b8ade169\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUGVvcGxlTmVzdFxccGVvcGxlbmVzdC11aVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGMzMWI4YWRlMTY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./src/components/providers/theme-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"PeopleNest - Enterprise HRMS Platform\",\n    description: \"Modern, AI-powered Human Resource Management System for enterprise organizations\",\n    keywords: [\n        \"HRMS\",\n        \"HR\",\n        \"Human Resources\",\n        \"Employee Management\",\n        \"Payroll\",\n        \"Performance\"\n    ],\n    manifest: \"/manifest.json\",\n    themeColor: \"#3b82f6\",\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1,\n        userScalable: false,\n        viewportFit: \"cover\"\n    },\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"PeopleNest HRMS\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-status-bar-style\": \"default\",\n        \"apple-mobile-web-app-title\": \"PeopleNest\",\n        \"application-name\": \"PeopleNest HRMS\",\n        \"msapplication-TileColor\": \"#3b82f6\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"PeopleNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"PeopleNest HRMS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased touch-manipulation`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(ssr)/./src/components/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/auth-layout-client.tsx */ \"(ssr)/./src/app/auth/auth-layout-client.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNhdXRoLWxheW91dC1jbGllbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aExheW91dENsaWVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoTGF5b3V0Q2xpZW50XCJdICovIFwiQzpcXFxcUGVvcGxlTmVzdFxcXFxwZW9wbGVuZXN0LXVpXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxhdXRoLWxheW91dC1jbGllbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cauth-layout-client.tsx%22%2C%22ids%22%3A%5B%22AuthLayoutClient%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/auth-layout-client.tsx":
/*!*********************************************!*\
  !*** ./src/app/auth/auth-layout-client.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthLayoutClient: () => (/* binding */ AuthLayoutClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./src/components/providers/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AuthLayoutClient auto */ \n\nfunction AuthLayoutClient({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"absolute bottom-4 left-0 right-0 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: \"\\xa9 2024 PeopleNest. All rights reserved. | Privacy Policy | Terms of Service\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\auth-layout-client.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvYXV0aC1sYXlvdXQtY2xpZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUVtRTtBQU01RCxTQUFTQyxpQkFBaUIsRUFBRUMsUUFBUSxFQUF5QjtJQUNsRSxxQkFDRSw4REFBQ0YsNkVBQVlBO2tCQUNYLDRFQUFDRztZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFHZiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pGOzs7Ozs7OEJBSUgsOERBQUNHO29CQUFPRCxXQUFVOzhCQUNoQiw0RUFBQ0U7d0JBQUVGLFdBQVU7a0NBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3ZEIiwic291cmNlcyI6WyJDOlxcUGVvcGxlTmVzdFxccGVvcGxlbmVzdC11aVxcc3JjXFxhcHBcXGF1dGhcXGF1dGgtbGF5b3V0LWNsaWVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvYXV0aC1wcm92aWRlclwiXG5cbmludGVyZmFjZSBBdXRoTGF5b3V0Q2xpZW50UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoTGF5b3V0Q2xpZW50KHsgY2hpbGRyZW4gfTogQXV0aExheW91dENsaWVudFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhQcm92aWRlcj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtd2hpdGUgdG8taW5kaWdvLTUwXCI+XG4gICAgICAgIHsvKiBCYWNrZ3JvdW5kIFBhdHRlcm4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmlkLXBhdHRlcm4gb3BhY2l0eS01XCIgLz5cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTQgbGVmdC0wIHJpZ2h0LTAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgwqkgMjAyNCBQZW9wbGVOZXN0LiBBbGwgcmlnaHRzIHJlc2VydmVkLiB8IFByaXZhY3kgUG9saWN5IHwgVGVybXMgb2YgU2VydmljZVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9mb290ZXI+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkF1dGhMYXlvdXRDbGllbnQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsImZvb3RlciIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/auth-layout-client.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle2,Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./src/components/providers/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// Form validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Password is required\").min(6, \"Password must be at least 6 characters\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean().default(false)\n});\nfunction LoginPage() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginSuccess, setLoginSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isAuthenticated, isLoading: authLoading } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_10__.useAuth)();\n    const { register, handleSubmit, formState: { errors, isSubmitting }, watch, trigger } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"<EMAIL>\",\n            password: \"awadhesh123\",\n            rememberMe: false\n        },\n        mode: \"onChange\"\n    });\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (isAuthenticated && !authLoading) {\n                router.push(\"/dashboard\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"LoginPage.useEffect.handleKeyDown\": (event)=>{\n                    // Alt + D to fill demo credentials in development\n                    if ( true && event.altKey && event.key === 'd') {\n                        event.preventDefault();\n                        const emailInput = document.querySelector('input[name=\"email\"]');\n                        const passwordInput = document.querySelector('input[name=\"password\"]');\n                        if (emailInput && passwordInput) {\n                            emailInput.value = '<EMAIL>';\n                            passwordInput.value = 'awadhesh123';\n                            emailInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                            passwordInput.dispatchEvent(new Event('input', {\n                                bubbles: true\n                            }));\n                        }\n                    }\n                }\n            }[\"LoginPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"LoginPage.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"LoginPage.useEffect\"];\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const onSubmit = async (data)=>{\n        try {\n            setLoginError(null);\n            setLoginSuccess(false);\n            const success = await login(data.email, data.password);\n            if (success) {\n                setLoginSuccess(true);\n                // Small delay to show success message before redirect\n                setTimeout(()=>{\n                    router.push(\"/dashboard\");\n                }, 1000);\n            } else {\n                setLoginError(\"Invalid email or password. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            setLoginError(\"An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Watch form values for real-time validation feedback\n    const watchedEmail = watch(\"email\");\n    const watchedPassword = watch(\"password\");\n    // Show loading state during auth check\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            initial: {\n                                scale: 0.8\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                delay: 0.2,\n                                duration: 0.3\n                            },\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-foreground mb-2\",\n                            children: \"PeopleNest\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Enterprise HRMS Platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2 mt-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"SOC 2 Compliant\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: \"Enterprise Ready\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"shadow-xl border-0 bg-white/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            className: \"space-y-1 pb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-2xl font-semibold text-center\",\n                                    children: \"Welcome back\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    className: \"text-center\",\n                                    children: \"Sign in to your account to continue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                loginSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        className: \"border-green-200 bg-green-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                className: \"text-green-800\",\n                                                children: \"Login successful! Redirecting to dashboard...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                        variant: \"destructive\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                                children: loginError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit(onSubmit),\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"email\"),\n                                                        type: \"email\",\n                                                        placeholder: \"Enter your email\",\n                                                        label: \"Email Address\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        error: errors.email?.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        ...register(\"password\"),\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        placeholder: \"Enter your password\",\n                                                        label: \"Password\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                            disabled: isSubmitting,\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 27\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle2_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        error: errors.password?.message,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ...register(\"rememberMe\"),\n                                                            type: \"checkbox\",\n                                                            className: \"rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50\",\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Remember me\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed\",\n                                                    onClick: (e)=>e.preventDefault(),\n                                                    children: \"Forgot password? (Coming Soon)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full h-11 text-base font-medium\",\n                                            loading: isSubmitting,\n                                            disabled: isSubmitting || loginSuccess,\n                                            children: isSubmitting ? \"Signing in...\" : loginSuccess ? \"Success!\" : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: \"Demo Credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"h-6 px-2 text-xs\",\n                                                    onClick: ()=>{\n                                                        const form = document.querySelector('form');\n                                                        const emailInput = form?.querySelector('input[name=\"email\"]');\n                                                        const passwordInput = form?.querySelector('input[name=\"password\"]');\n                                                        if (emailInput && passwordInput) {\n                                                            emailInput.value = '<EMAIL>';\n                                                            passwordInput.value = 'awadhesh123';\n                                                            emailInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                            passwordInput.dispatchEvent(new Event('input', {\n                                                                bubbles: true\n                                                            }));\n                                                        }\n                                                    },\n                                                    disabled: isSubmitting,\n                                                    children: \"Quick Fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Email:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" <EMAIL>\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Password:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" awadhesh123\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-600 mt-2\",\n                                                    children: \"Super Admin credentials - pre-filled for development\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-500 mt-1\",\n                                                    children: \"Tip: Press Alt + D to quick fill\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed\",\n                                                onClick: (e)=>e.preventDefault(),\n                                                children: \"Contact your administrator\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.8,\n                        duration: 0.3\n                    },\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: \"Protected by enterprise-grade security and encryption\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        token: null\n    });\n    // Initialize auth state from storage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        const storedToken = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                        const storedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredUser)();\n                        if (storedToken && storedUser) {\n                            // Verify token is still valid by calling /me endpoint\n                            const isValid = await verifyToken(storedToken);\n                            if (isValid) {\n                                setAuthState({\n                                    user: storedUser,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    token: storedToken\n                                });\n                            } else {\n                                // Token is invalid, clear storage\n                                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n                                setAuthState({\n                                    user: null,\n                                    isAuthenticated: false,\n                                    isLoading: false,\n                                    token: null\n                                });\n                            }\n                        } else {\n                            setAuthState({\n                                user: null,\n                                isAuthenticated: false,\n                                isLoading: false,\n                                token: null\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to initialize auth:', error);\n                        setAuthState({\n                            user: null,\n                            isAuthenticated: false,\n                            isLoading: false,\n                            token: null\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const verifyToken = async (token)=>{\n        try {\n            const response = await fetch(`${\"http://localhost:3002\"}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.ME}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.error('Token verification failed:', error);\n            return false;\n        }\n    };\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[login]\": async (email, password)=>{\n            try {\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: true\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                const response = await fetch(`${\"http://localhost:3002\"}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.LOGIN}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { user, tokens, sessionId, deviceFingerprint } = data;\n                    // Store both access and refresh tokens\n                    const tokenData = {\n                        accessToken: tokens.accessToken,\n                        refreshToken: tokens.refreshToken,\n                        expiresIn: tokens.expiresIn,\n                        tokenType: tokens.tokenType,\n                        sessionId,\n                        deviceFingerprint\n                    };\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(JSON.stringify(tokenData));\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n                    setAuthState({\n                        user,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        token: tokens.accessToken\n                    });\n                    return true;\n                } else {\n                    const errorData = await response.json().catch({\n                        \"AuthProvider.useCallback[login]\": ()=>({})\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    console.error('Login failed:', errorData);\n                    setAuthState({\n                        \"AuthProvider.useCallback[login]\": (prev)=>({\n                                ...prev,\n                                isLoading: false\n                            })\n                    }[\"AuthProvider.useCallback[login]\"]);\n                    return false;\n                }\n            } catch (error) {\n                console.error('Login failed:', error);\n                setAuthState({\n                    \"AuthProvider.useCallback[login]\": (prev)=>({\n                            ...prev,\n                            isLoading: false\n                        })\n                }[\"AuthProvider.useCallback[login]\"]);\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[login]\"], []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": async ()=>{\n            try {\n                const storedTokenData = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                let tokenData = null;\n                if (storedTokenData) {\n                    try {\n                        tokenData = JSON.parse(storedTokenData);\n                    } catch  {\n                        // If token data is not JSON, treat as legacy token\n                        tokenData = {\n                            accessToken: storedTokenData\n                        };\n                    }\n                }\n                // Call backend logout endpoint\n                if (tokenData?.accessToken) {\n                    try {\n                        await fetch(`${\"http://localhost:3002\"}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.LOGOUT}`, {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': `Bearer ${tokenData.accessToken}`\n                            },\n                            body: JSON.stringify({\n                                refreshToken: tokenData.refreshToken,\n                                sessionId: tokenData.sessionId\n                            })\n                        });\n                    } catch (error) {\n                        console.warn('Logout API call failed:', error);\n                    // Continue with local logout even if API call fails\n                    }\n                }\n            } catch (error) {\n                console.warn('Error during logout:', error);\n            } finally{\n                // Always clear local state regardless of API call result\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.removeStoredToken)();\n                setAuthState({\n                    user: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    token: null\n                });\n                // Redirect to login page\n                if (false) {}\n            }\n        }\n    }[\"AuthProvider.useCallback[logout]\"], []);\n    const refreshToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshToken]\": async ()=>{\n            try {\n                const storedTokenData = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getStoredToken)();\n                if (!storedTokenData) {\n                    return false;\n                }\n                let tokenData;\n                try {\n                    tokenData = JSON.parse(storedTokenData);\n                } catch  {\n                    // Legacy token format - try to use as access token\n                    tokenData = {\n                        accessToken: storedTokenData\n                    };\n                }\n                if (!tokenData.refreshToken) {\n                    // No refresh token available, logout\n                    logout();\n                    return false;\n                }\n                const response = await fetch(`${\"http://localhost:3002\"}${_lib_auth__WEBPACK_IMPORTED_MODULE_2__.AUTH_ENDPOINTS.REFRESH}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        refreshToken: tokenData.refreshToken,\n                        deviceFingerprint: tokenData.deviceFingerprint\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    const { tokens } = data;\n                    // Update stored token data\n                    const newTokenData = {\n                        ...tokenData,\n                        accessToken: tokens.accessToken,\n                        refreshToken: tokens.refreshToken,\n                        expiresIn: tokens.expiresIn\n                    };\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredToken)(JSON.stringify(newTokenData));\n                    setAuthState({\n                        \"AuthProvider.useCallback[refreshToken]\": (prev)=>({\n                                ...prev,\n                                token: tokens.accessToken\n                            })\n                    }[\"AuthProvider.useCallback[refreshToken]\"]);\n                    return true;\n                } else {\n                    logout();\n                    return false;\n                }\n            } catch (error) {\n                console.error('Token refresh failed:', error);\n                logout();\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshToken]\"], [\n        logout\n    ]);\n    const updateUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateUser]\": (user)=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.setStoredUser)(user);\n            setAuthState({\n                \"AuthProvider.useCallback[updateUser]\": (prev)=>({\n                        ...prev,\n                        user\n                    })\n            }[\"AuthProvider.useCallback[updateUser]\"]);\n        }\n    }[\"AuthProvider.useCallback[updateUser]\"], []);\n    const value = {\n        ...authState,\n        login,\n        logout,\n        refreshToken,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(ssr)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Provide a fallback when used outside of ThemeProvider\n        return {\n            theme: 'system',\n            setTheme: ()=>{},\n            toggleTheme: ()=>{},\n            systemTheme: 'light',\n            actualTheme: 'light'\n        };\n    }\n    return context;\n}\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [systemTheme, setSystemTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get the actual theme being applied\n    const actualTheme = theme === 'system' ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Initialize theme on mount\n            const storedTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)();\n            setThemeState(storedTheme);\n            // Detect system theme\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n            // Set mounted first to prevent hydration mismatch\n            setMounted(true);\n            // Note: Theme is already applied by the script in root layout\n            // We don't need to apply it again here to prevent hydration issues\n            // Listen for system theme changes\n            const handleSystemThemeChange = {\n                \"ThemeProvider.useEffect.handleSystemThemeChange\": (e)=>{\n                    setSystemTheme(e.matches ? 'dark' : 'light');\n                    // If using system theme, reapply it\n                    if ((0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)() === 'system') {\n                        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)('system');\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleSystemThemeChange\"];\n            mediaQuery.addEventListener('change', handleSystemThemeChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleSystemThemeChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const handleSetTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.setThemeMode)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n    };\n    const handleToggleTheme = ()=>{\n        const currentActualTheme = theme === 'system' ? systemTheme : theme;\n        const newTheme = currentActualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    // Note: We don't prevent rendering to avoid hydration mismatch\n    // The theme is already applied by the script in root layout\n    const value = {\n        theme,\n        setTheme: handleSetTheme,\n        toggleTheme: handleToggleTheme,\n        systemTheme,\n        actualTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n            warning: \"border-yellow-500/50 text-yellow-900 bg-yellow-50 dark:border-yellow-500 dark:text-yellow-100 dark:bg-yellow-950 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400\",\n            success: \"border-green-500/50 text-green-900 bg-green-50 dark:border-green-500 dark:text-green-100 dark:bg-green-950 [&>svg]:text-green-600 dark:[&>svg]:text-green-400\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400\",\n            warning: \"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400\",\n            info: \"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400\",\n            // HRMS specific variants\n            active: \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n            inactive: \"border-transparent bg-muted text-muted-foreground\",\n            pending: \"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400\",\n            approved: \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n            rejected: \"border-transparent bg-red-500/10 text-red-600 dark:text-red-400\",\n            draft: \"border-transparent bg-muted text-muted-foreground\"\n        },\n        size: {\n            default: \"px-2.5 py-0.5 text-xs\",\n            sm: \"px-2 py-0.5 text-xs\",\n            lg: \"px-3 py-1 text-sm\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Badge({ className, variant, size, icon, removable, onRemove, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant,\n            size\n        }), className),\n        ...props,\n        children: [\n            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                lineNumber: 70,\n                columnNumber: 16\n            }, this),\n            children,\n            removable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10\",\n                onClick: onRemove,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-3 w-3\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9iYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ21DO0FBQ2pDO0FBRWhDLE1BQU1HLGdCQUFnQkYsNkRBQUdBLENBQ3ZCLDBLQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUNFO1lBQ0ZDLFdBQ0U7WUFDRkMsYUFDRTtZQUNGQyxTQUFTO1lBQ1RDLFNBQ0U7WUFDRkMsU0FDRTtZQUNGQyxNQUNFO1lBQ0YseUJBQXlCO1lBQ3pCQyxRQUNFO1lBQ0ZDLFVBQ0U7WUFDRkMsU0FDRTtZQUNGQyxVQUNFO1lBQ0ZDLFVBQ0U7WUFDRkMsT0FDRTtRQUNKO1FBQ0FDLE1BQU07WUFDSmIsU0FBUztZQUNUYyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmakIsU0FBUztRQUNUYyxNQUFNO0lBQ1I7QUFDRjtBQVdGLFNBQVNJLE1BQU0sRUFDYkMsU0FBUyxFQUNUbkIsT0FBTyxFQUNQYyxJQUFJLEVBQ0pNLElBQUksRUFDSkMsU0FBUyxFQUNUQyxRQUFRLEVBQ1JDLFFBQVEsRUFDUixHQUFHQyxPQUNRO0lBQ1gscUJBQ0UsOERBQUNDO1FBQUlOLFdBQVd0Qiw4Q0FBRUEsQ0FBQ0MsY0FBYztZQUFFRTtZQUFTYztRQUFLLElBQUlLO1FBQWEsR0FBR0ssS0FBSzs7WUFDdkVKLHNCQUFRLDhEQUFDTTtnQkFBS1AsV0FBVTswQkFBUUM7Ozs7OztZQUNoQ0c7WUFDQUYsMkJBQ0MsOERBQUNNO2dCQUNDQyxNQUFLO2dCQUNMVCxXQUFVO2dCQUNWVSxTQUFTUDswQkFFVCw0RUFBQ1E7b0JBQ0NYLFdBQVU7b0JBQ1ZZLE1BQUs7b0JBQ0xDLFFBQU87b0JBQ1BDLFNBQVE7OEJBRVIsNEVBQUNDO3dCQUNDQyxlQUFjO3dCQUNkQyxnQkFBZTt3QkFDZkMsYUFBYTt3QkFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9oQjtBQUUrQiIsInNvdXJjZXMiOlsiQzpcXFBlb3BsZU5lc3RcXHBlb3BsZW5lc3QtdWlcXHNyY1xcY29tcG9uZW50c1xcdWlcXGJhZGdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBiYWRnZVZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWZ1bGwgYm9yZGVyIHB4LTIuNSBweS0wLjUgdGV4dC14cyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yaW5nIGZvY3VzOnJpbmctb2Zmc2V0LTJcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzgwXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJvcmRlci10cmFuc3BhcmVudCBiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1zZWNvbmRhcnkvODBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGhvdmVyOmJnLWRlc3RydWN0aXZlLzgwXCIsXG4gICAgICAgIG91dGxpbmU6IFwidGV4dC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIHN1Y2Nlc3M6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctZ3JlZW4tNTAwLzEwIHRleHQtZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTUwMC8yMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCIsXG4gICAgICAgIHdhcm5pbmc6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmcteWVsbG93LTUwMC8xMCB0ZXh0LXllbGxvdy02MDAgaG92ZXI6YmcteWVsbG93LTUwMC8yMCBkYXJrOnRleHQteWVsbG93LTQwMFwiLFxuICAgICAgICBpbmZvOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWJsdWUtNTAwLzEwIHRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MDAvMjAgZGFyazp0ZXh0LWJsdWUtNDAwXCIsXG4gICAgICAgIC8vIEhSTVMgc3BlY2lmaWMgdmFyaWFudHNcbiAgICAgICAgYWN0aXZlOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWdyZWVuLTUwMC8xMCB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCIsXG4gICAgICAgIGluYWN0aXZlOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLW11dGVkIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBwZW5kaW5nOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLXllbGxvdy01MDAvMTAgdGV4dC15ZWxsb3ctNjAwIGRhcms6dGV4dC15ZWxsb3ctNDAwXCIsXG4gICAgICAgIGFwcHJvdmVkOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWdyZWVuLTUwMC8xMCB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCIsXG4gICAgICAgIHJlamVjdGVkOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLXJlZC01MDAvMTAgdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCIsXG4gICAgICAgIGRyYWZ0OlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLW11dGVkIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJweC0yLjUgcHktMC41IHRleHQteHNcIixcbiAgICAgICAgc206IFwicHgtMiBweS0wLjUgdGV4dC14c1wiLFxuICAgICAgICBsZzogXCJweC0zIHB5LTEgdGV4dC1zbVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQmFkZ2VQcm9wc1xuICBleHRlbmRzIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJhZGdlVmFyaWFudHM+IHtcbiAgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZVxuICByZW1vdmFibGU/OiBib29sZWFuXG4gIG9uUmVtb3ZlPzogKCkgPT4gdm9pZFxufVxuXG5mdW5jdGlvbiBCYWRnZSh7IFxuICBjbGFzc05hbWUsIFxuICB2YXJpYW50LCBcbiAgc2l6ZSwgXG4gIGljb24sIFxuICByZW1vdmFibGUsIFxuICBvblJlbW92ZSwgXG4gIGNoaWxkcmVuLFxuICAuLi5wcm9wcyBcbn06IEJhZGdlUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y24oYmFkZ2VWYXJpYW50cyh7IHZhcmlhbnQsIHNpemUgfSksIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30+XG4gICAgICB7aWNvbiAmJiA8c3BhbiBjbGFzc05hbWU9XCJtci0xXCI+e2ljb259PC9zcGFuPn1cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIHtyZW1vdmFibGUgJiYgKFxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwibWwtMSBpbmxpbmUtZmxleCBoLTQgdy00IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgaG92ZXI6YmctYmxhY2svMTBcIlxuICAgICAgICAgIG9uQ2xpY2s9e29uUmVtb3ZlfVxuICAgICAgICA+XG4gICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0zIHctM1wiXG4gICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCB7IEJhZGdlLCBiYWRnZVZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImN2YSIsImNuIiwiYmFkZ2VWYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJzZWNvbmRhcnkiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzdWNjZXNzIiwid2FybmluZyIsImluZm8iLCJhY3RpdmUiLCJpbmFjdGl2ZSIsInBlbmRpbmciLCJhcHByb3ZlZCIsInJlamVjdGVkIiwiZHJhZnQiLCJzaXplIiwic20iLCJsZyIsImRlZmF1bHRWYXJpYW50cyIsIkJhZGdlIiwiY2xhc3NOYW1lIiwiaWNvbiIsInJlbW92YWJsZSIsIm9uUmVtb3ZlIiwiY2hpbGRyZW4iLCJwcm9wcyIsImRpdiIsInNwYW4iLCJidXR0b24iLCJ0eXBlIiwib25DbGljayIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            success: \"bg-green-600 text-white hover:bg-green-700\",\n            warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 70,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 92,\n                columnNumber: 11\n            }, undefined),\n            children,\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 96,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-border\",\n            elevated: \"shadow-md\",\n            outlined: \"border-2\",\n            ghost: \"border-transparent shadow-none\"\n        },\n        padding: {\n            none: \"\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        padding: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, padding, hover = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(cardVariants({\n            variant,\n            padding\n        }), hover && \"transition-shadow hover:shadow-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   inputVariants: () => (/* binding */ inputVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n\n\n\n\nconst inputVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"\",\n            error: \"border-red-500 focus-visible:ring-red-500\",\n            success: \"border-green-500 focus-visible:ring-green-500\"\n        },\n        size: {\n            default: \"h-10\",\n            sm: \"h-9 px-2 text-xs\",\n            lg: \"h-11 px-4\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, variant, size, leftIcon, rightIcon, error, label, helperText, id, ...props }, ref)=>{\n    const inputId = id || react__WEBPACK_IMPORTED_MODULE_1__.useId();\n    const hasError = !!error;\n    const finalVariant = hasError ? \"error\" : variant;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: \"block text-sm font-medium text-foreground mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 58,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\",\n                        children: leftIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: type,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(inputVariants({\n                            variant: finalVariant,\n                            size,\n                            className\n                        }), leftIcon && \"pl-10\", rightIcon && \"pr-10\"),\n                        ref: ref,\n                        id: inputId,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\",\n                        children: rightIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined),\n            (error || helperText) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-1 text-xs\", hasError ? \"text-destructive\" : \"text-muted-foreground\"),\n                children: error || helperText\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n                lineNumber: 89,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 56,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   AUTH_ENDPOINTS: () => (/* binding */ AUTH_ENDPOINTS),\n/* harmony export */   MOCK_USER: () => (/* binding */ MOCK_USER),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   TOKEN_KEY: () => (/* binding */ TOKEN_KEY),\n/* harmony export */   USER_KEY: () => (/* binding */ USER_KEY),\n/* harmony export */   canAccessAdminModule: () => (/* binding */ canAccessAdminModule),\n/* harmony export */   canAccessHRModule: () => (/* binding */ canAccessHRModule),\n/* harmony export */   canAccessOrganizationModule: () => (/* binding */ canAccessOrganizationModule),\n/* harmony export */   canManageUsers: () => (/* binding */ canManageUsers),\n/* harmony export */   canReadAllEmployees: () => (/* binding */ canReadAllEmployees),\n/* harmony export */   canReadDepartments: () => (/* binding */ canReadDepartments),\n/* harmony export */   canReadPositions: () => (/* binding */ canReadPositions),\n/* harmony export */   canWriteAllEmployees: () => (/* binding */ canWriteAllEmployees),\n/* harmony export */   canWriteDepartments: () => (/* binding */ canWriteDepartments),\n/* harmony export */   canWritePositions: () => (/* binding */ canWritePositions),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getStoredToken: () => (/* binding */ getStoredToken),\n/* harmony export */   getStoredTokenData: () => (/* binding */ getStoredTokenData),\n/* harmony export */   getStoredUser: () => (/* binding */ getStoredUser),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasAnyRole: () => (/* binding */ hasAnyRole),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isRoleAtLeast: () => (/* binding */ isRoleAtLeast),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   refreshToken: () => (/* binding */ refreshToken),\n/* harmony export */   removeStoredToken: () => (/* binding */ removeStoredToken),\n/* harmony export */   setStoredToken: () => (/* binding */ setStoredToken),\n/* harmony export */   setStoredUser: () => (/* binding */ setStoredUser)\n/* harmony export */ });\n// Authentication and authorization utilities\n// Permission hierarchy - higher roles inherit lower role permissions\nconst ROLE_PERMISSIONS = {\n    employee: [\n        'employee_read_own',\n        'employee_update_own'\n    ],\n    manager: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team'\n    ],\n    hr: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read'\n    ],\n    hr_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management'\n    ],\n    super_admin: [\n        'employee_read_own',\n        'employee_update_own',\n        'manager',\n        'employee_read_team',\n        'employee_update_team',\n        'hr',\n        'employee_read_all',\n        'employee_update_all',\n        'department_read',\n        'position_read',\n        'hr_admin',\n        'department_write',\n        'position_write',\n        'user_management',\n        'super_admin',\n        'system_admin'\n    ]\n};\n// Token management\nconst TOKEN_KEY = 'peoplenest_auth_token';\nconst USER_KEY = 'peoplenest_user';\nfunction getStoredToken() {\n    if (true) return null;\n    const tokenStr = localStorage.getItem(TOKEN_KEY);\n    if (!tokenStr) return null;\n    try {\n        // Try to parse as JSON (new format)\n        const tokenData = JSON.parse(tokenStr);\n        return tokenData.accessToken || null;\n    } catch  {\n        // If parsing fails, assume it's a legacy token string\n        return tokenStr;\n    }\n}\nfunction setStoredToken(token) {\n    if (true) return;\n    localStorage.setItem(TOKEN_KEY, token);\n}\nfunction removeStoredToken() {\n    if (true) return;\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n}\nfunction getStoredTokenData() {\n    if (true) return null;\n    const tokenStr = localStorage.getItem(TOKEN_KEY);\n    if (!tokenStr) return null;\n    try {\n        return JSON.parse(tokenStr);\n    } catch  {\n        return null;\n    }\n}\nfunction isTokenExpired(tokenData) {\n    if (!tokenData.expiresIn) return true;\n    // Add some buffer time (5 minutes) before actual expiry\n    const bufferTime = 5 * 60 * 1000 // 5 minutes in milliseconds\n    ;\n    const expiryTime = Date.now() + tokenData.expiresIn * 1000 - bufferTime;\n    return Date.now() >= expiryTime;\n}\nfunction getStoredUser() {\n    if (true) return null;\n    const userStr = localStorage.getItem(USER_KEY);\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n}\nfunction setStoredUser(user) {\n    if (true) return;\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n}\n// Permission checking utilities\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasRole(user, role) {\n    if (!user) return false;\n    return user.role === role;\n}\nfunction hasAnyRole(user, roles) {\n    if (!user) return false;\n    return roles.includes(user.role);\n}\nfunction isRoleAtLeast(user, minRole) {\n    if (!user) return false;\n    const roleHierarchy = [\n        'employee',\n        'manager',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ];\n    const userRoleIndex = roleHierarchy.indexOf(user.role);\n    const minRoleIndex = roleHierarchy.indexOf(minRole);\n    return userRoleIndex >= minRoleIndex;\n}\n// Department and organization permissions\nfunction canReadDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteDepartments(user) {\n    return hasAnyPermission(user, [\n        'department_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadPositions(user) {\n    return hasAnyPermission(user, [\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWritePositions(user) {\n    return hasAnyPermission(user, [\n        'position_write',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canReadAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_read_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canWriteAllEmployees(user) {\n    return hasAnyPermission(user, [\n        'employee_update_all',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canManageUsers(user) {\n    return hasAnyPermission(user, [\n        'user_management',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Navigation permissions\nfunction canAccessOrganizationModule(user) {\n    return hasAnyPermission(user, [\n        'department_read',\n        'position_read',\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessHRModule(user) {\n    return hasAnyPermission(user, [\n        'hr',\n        'hr_admin',\n        'super_admin'\n    ]);\n}\nfunction canAccessAdminModule(user) {\n    return hasAnyPermission(user, [\n        'hr_admin',\n        'super_admin'\n    ]);\n}\n// Mock user for development (remove in production)\nconst MOCK_USER = {\n    id: '1',\n    employeeId: 'EMP001',\n    email: '<EMAIL>',\n    firstName: 'Admin',\n    lastName: 'User',\n    role: 'hr_admin',\n    permissions: ROLE_PERMISSIONS.hr_admin,\n    departmentId: '1',\n    managerId: undefined\n};\n// API endpoints\nconst AUTH_ENDPOINTS = {\n    LOGIN: '/api/auth/login',\n    LOGOUT: '/api/auth/logout',\n    REFRESH: '/api/auth/refresh',\n    ME: '/api/auth/me'\n};\n// API Configuration\nconst API_CONFIG = {\n    BASE_URL: \"http://localhost:3002\" || 0,\n    API_BASE_URL: \"http://localhost:3002/api\" || 0,\n    TIMEOUT: 30000,\n    RETRY_ATTEMPTS: 3,\n    RETRY_DELAY: 1000\n};\n// Authentication API functions\nasync function loginUser(email, password) {\n    const response = await fetch(`${API_CONFIG.BASE_URL}${AUTH_ENDPOINTS.LOGIN}`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            email,\n            password\n        })\n    });\n    if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Login failed');\n    }\n    const data = await response.json();\n    return data;\n}\nasync function logoutUser(token) {\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    if (token) {\n        headers.Authorization = `Bearer ${token}`;\n    }\n    try {\n        await fetch(`${API_CONFIG.BASE_URL}${AUTH_ENDPOINTS.LOGOUT}`, {\n            method: 'POST',\n            headers\n        });\n    } catch (error) {\n        // Logout should succeed even if the API call fails\n        console.warn('Logout API call failed:', error);\n    }\n}\nasync function refreshToken(refreshToken) {\n    const response = await fetch(`${API_CONFIG.BASE_URL}${AUTH_ENDPOINTS.REFRESH}`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            refreshToken\n        })\n    });\n    if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Token refresh failed');\n    }\n    const data = await response.json();\n    return data;\n}\nasync function getCurrentUser(token) {\n    const response = await fetch(`${API_CONFIG.BASE_URL}${AUTH_ENDPOINTS.ME}`, {\n        method: 'GET',\n        headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`\n        }\n    });\n    if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Failed to get user info');\n    }\n    const data = await response.json();\n    return data.user;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getContrastTextColor: () => (/* binding */ getContrastTextColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getThemeMode: () => (/* binding */ getThemeMode),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   setThemeMode: () => (/* binding */ setThemeMode),\n/* harmony export */   statusColors: () => (/* binding */ statusColors),\n/* harmony export */   themeColors: () => (/* binding */ themeColors),\n/* harmony export */   toggleTheme: () => (/* binding */ toggleTheme),\n/* harmony export */   useSystemTheme: () => (/* binding */ useSystemTheme)\n/* harmony export */ });\n// Theme utilities for PeopleNest HRMS\n// Provides consistent color management and theme switching capabilities\nconst themeColors = {\n    // Light mode colors\n    light: {\n        background: '#ffffff',\n        foreground: '#171717',\n        card: '#ffffff',\n        cardForeground: '#171717',\n        popover: '#ffffff',\n        popoverForeground: '#171717',\n        primary: '#3b82f6',\n        primaryForeground: '#ffffff',\n        secondary: '#f1f5f9',\n        secondaryForeground: '#0f172a',\n        muted: '#f1f5f9',\n        mutedForeground: '#64748b',\n        accent: '#f1f5f9',\n        accentForeground: '#0f172a',\n        destructive: '#ef4444',\n        destructiveForeground: '#ffffff',\n        border: '#e2e8f0',\n        input: '#e2e8f0',\n        ring: '#3b82f6',\n        // Enhanced semantic colors\n        textPrimary: '#171717',\n        textSecondary: '#64748b',\n        textTertiary: '#94a3b8',\n        textInverse: '#ffffff',\n        surfacePrimary: '#ffffff',\n        surfaceSecondary: '#f8fafc',\n        surfaceTertiary: '#f1f5f9',\n        surfaceHover: '#f8fafc',\n        surfaceActive: '#e2e8f0',\n        success: '#22c55e',\n        successForeground: '#ffffff',\n        warning: '#f59e0b',\n        warningForeground: '#ffffff',\n        info: '#3b82f6',\n        infoForeground: '#ffffff',\n        // Chart colors\n        chart1: '#3b82f6',\n        chart2: '#10b981',\n        chart3: '#f59e0b',\n        chart4: '#ef4444',\n        chart5: '#8b5cf6',\n        chart6: '#06b6d4',\n        chart7: '#84cc16',\n        chart8: '#f97316',\n        // Status colors\n        statusMeeting: '#3b82f6',\n        statusDeadline: '#ef4444',\n        statusEvent: '#10b981',\n        statusActive: '#22c55e',\n        statusPending: '#f59e0b',\n        statusInactive: '#6b7280'\n    },\n    // Dark mode colors\n    dark: {\n        background: '#0a0a0a',\n        foreground: '#ededed',\n        card: '#111111',\n        cardForeground: '#ededed',\n        popover: '#111111',\n        popoverForeground: '#ededed',\n        primary: '#60a5fa',\n        primaryForeground: '#0a0a0a',\n        secondary: '#1e293b',\n        secondaryForeground: '#ededed',\n        muted: '#1e293b',\n        mutedForeground: '#94a3b8',\n        accent: '#1e293b',\n        accentForeground: '#ededed',\n        destructive: '#dc2626',\n        destructiveForeground: '#ededed',\n        border: '#27272a',\n        input: '#27272a',\n        ring: '#60a5fa',\n        // Enhanced semantic colors\n        textPrimary: '#ededed',\n        textSecondary: '#a1a1aa',\n        textTertiary: '#71717a',\n        textInverse: '#0a0a0a',\n        surfacePrimary: '#0a0a0a',\n        surfaceSecondary: '#111111',\n        surfaceTertiary: '#1a1a1a',\n        surfaceHover: '#1e1e1e',\n        surfaceActive: '#27272a',\n        success: '#22c55e',\n        successForeground: '#0a0a0a',\n        warning: '#f59e0b',\n        warningForeground: '#0a0a0a',\n        info: '#60a5fa',\n        infoForeground: '#0a0a0a',\n        // Chart colors - adjusted for dark theme\n        chart1: '#60a5fa',\n        chart2: '#34d399',\n        chart3: '#fbbf24',\n        chart4: '#f87171',\n        chart5: '#a78bfa',\n        chart6: '#22d3ee',\n        chart7: '#a3e635',\n        chart8: '#fb923c',\n        // Status colors - adjusted for dark theme\n        statusMeeting: '#60a5fa',\n        statusDeadline: '#f87171',\n        statusEvent: '#34d399',\n        statusActive: '#34d399',\n        statusPending: '#fbbf24',\n        statusInactive: '#9ca3af'\n    }\n};\n/**\n * Utility function to get the current theme mode\n */ function getThemeMode() {\n    if (true) return 'system';\n    const stored = localStorage.getItem('theme-mode');\n    if (stored === 'light' || stored === 'dark') return stored;\n    return 'system';\n}\n/**\n * Utility function to set the theme mode\n */ function setThemeMode(mode) {\n    if (true) return;\n    if (mode === 'system') {\n        localStorage.removeItem('theme-mode');\n    } else {\n        localStorage.setItem('theme-mode', mode);\n    }\n}\n/**\n * Utility function to toggle between light and dark modes\n */ function toggleTheme() {\n    const current = getThemeMode();\n    const next = current === 'light' ? 'dark' : 'light';\n    setThemeMode(next);\n}\n/**\n * Hook to detect system theme preference\n */ function useSystemTheme() {\n    if (true) return 'light';\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n}\n/**\n * Apply theme to document element\n */ function applyTheme(mode) {\n    if (true) return;\n    const root = document.documentElement;\n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    if (mode === 'system') {\n        // Use system preference\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        root.classList.add(systemTheme);\n    } else {\n        // Use explicit theme\n        root.classList.add(mode);\n    }\n}\n/**\n * Initialize theme on app load\n */ function initializeTheme() {\n    if (true) return;\n    const stored = localStorage.getItem('theme-mode');\n    const mode = stored || 'system';\n    applyTheme(mode);\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = ()=>{\n        if (getThemeMode() === 'system') {\n            applyTheme('system');\n        }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return ()=>mediaQuery.removeEventListener('change', handleChange);\n}\n/**\n * Utility to get contrast-safe text color for a given background\n */ function getContrastTextColor(backgroundColor, theme = 'light') {\n    // Simple heuristic - in a real app you might want to use a proper contrast calculation\n    const colors = themeColors[theme];\n    // For dark backgrounds, use light text\n    if (backgroundColor.includes('dark') || backgroundColor.includes('black') || backgroundColor === colors.primary || backgroundColor === colors.destructive) {\n        return colors.textInverse;\n    }\n    // For light backgrounds, use dark text\n    return colors.textPrimary;\n}\n/**\n * Status color mappings for consistent UI\n */ const statusColors = {\n    active: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    inactive: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    },\n    pending: {\n        light: 'bg-yellow-500/10 text-yellow-600',\n        dark: 'bg-yellow-500/10 text-yellow-400'\n    },\n    approved: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    rejected: {\n        light: 'bg-red-500/10 text-red-600',\n        dark: 'bg-red-500/10 text-red-400'\n    },\n    draft: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    }\n};\n/**\n * Get status color classes for current theme\n */ function getStatusColor(status, theme = 'light') {\n    return statusColors[status][theme];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/theme.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        month: \"long\",\n        day: \"numeric\",\n        year: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatPercentage(value) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits: 1,\n        maximumFractionDigits: 1\n    }).format(value / 100);\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/zod","vendor-chunks/react-hook-form","vendor-chunks/@hookform"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();