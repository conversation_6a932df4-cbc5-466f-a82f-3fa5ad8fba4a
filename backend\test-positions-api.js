const axios = require('axios');

async function testPositionsAPI() {
  console.log('🔍 Testing Positions API');
  console.log('==================================================');
  
  try {
    // Step 1: Login to get access token
    console.log('📤 Step 1: Login to get access token...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      email: '<EMAIL>',
      password: 'awadhesh123'
    });
    
    const accessToken = loginResponse.data.tokens.accessToken;
    console.log('✅ Login successful!');
    console.log(`🔑 Access Token (first 50 chars): ${accessToken.substring(0, 50)}...`);
    
    // Step 2: Test positions endpoint
    console.log('\n📤 Step 2: Testing /api/positions endpoint...');
    const positionsResponse = await axios.get('http://localhost:3002/api/positions', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    console.log('✅ Positions API successful!');
    console.log('📋 Positions Response:');
    console.log(JSON.stringify(positionsResponse.data, null, 2));
    
    // Step 3: Test departments endpoint
    console.log('\n📤 Step 3: Testing /api/departments endpoint...');
    const departmentsResponse = await axios.get('http://localhost:3002/api/departments', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    console.log('✅ Departments API successful!');
    console.log('📋 Departments Response:');
    console.log(JSON.stringify(departmentsResponse.data, null, 2));
    
    console.log('\n✅ All API tests passed!');
    console.log('🔧 Frontend should now work correctly.');
    
  } catch (error) {
    console.log('❌ Error in API test:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testPositionsAPI();
