"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeService = void 0;
const databaseService_1 = require("./databaseService");
const logger_1 = require("../utils/logger");
const uuid_1 = require("uuid");
class EmployeeService {
    constructor() {
        this.db = new databaseService_1.DatabaseService();
        this.encryptionKey = process.env.ENCRYPTION_KEY || 'default_key_change_in_production';
    }
    async decryptEmployeeData(rawEmployee) {
        try {
            let firstName = null;
            let lastName = null;
            let phone = null;
            let dateOfBirth = null;
            let salary = '0';
            if (rawEmployee.first_name_encrypted) {
                const result = await this.db.query('SELECT pgp_sym_decrypt($1, $2) as value', [rawEmployee.first_name_encrypted, this.encryptionKey]);
                firstName = result.rows[0]?.value;
            }
            if (rawEmployee.last_name_encrypted) {
                const result = await this.db.query('SELECT pgp_sym_decrypt($1, $2) as value', [rawEmployee.last_name_encrypted, this.encryptionKey]);
                lastName = result.rows[0]?.value;
            }
            if (rawEmployee.phone_encrypted) {
                const result = await this.db.query('SELECT pgp_sym_decrypt($1, $2) as value', [rawEmployee.phone_encrypted, this.encryptionKey]);
                phone = result.rows[0]?.value;
            }
            if (rawEmployee.date_of_birth_encrypted) {
                const result = await this.db.query('SELECT pgp_sym_decrypt($1, $2) as value', [rawEmployee.date_of_birth_encrypted, this.encryptionKey]);
                dateOfBirth = result.rows[0]?.value;
            }
            if (rawEmployee.salary_encrypted) {
                const result = await this.db.query('SELECT pgp_sym_decrypt($1, $2) as value', [rawEmployee.salary_encrypted, this.encryptionKey]);
                salary = result.rows[0]?.value || '0';
            }
            const decrypted = {
                first_name: firstName,
                last_name: lastName,
                phone: phone,
                date_of_birth: dateOfBirth,
                salary: salary
            };
            return {
                id: rawEmployee.id,
                firstName: decrypted.first_name,
                lastName: decrypted.last_name,
                email: rawEmployee.email,
                personalEmail: rawEmployee.personal_email,
                phone: decrypted.phone,
                dateOfBirth: decrypted.date_of_birth,
                gender: rawEmployee.gender,
                address: null,
                emergencyContact: null,
                departmentId: rawEmployee.department_id,
                positionId: rawEmployee.position_id,
                managerId: rawEmployee.manager_id,
                employeeType: rawEmployee.employment_type,
                hireDate: rawEmployee.hire_date,
                salary: parseFloat(decrypted.salary) || 0,
                currency: rawEmployee.currency,
                workLocation: rawEmployee.work_location,
                skills: rawEmployee.skills || [],
                certifications: rawEmployee.certifications || [],
                status: rawEmployee.status,
                createdAt: rawEmployee.created_at,
                updatedAt: rawEmployee.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error decrypting employee data:', error);
            throw new Error('Failed to decrypt employee data');
        }
    }
    async getEmployees(filters, user) {
        try {
            const page = parseInt(filters.page) || 1;
            const limit = Math.min(parseInt(filters.limit) || 20, 100);
            const offset = (page - 1) * limit;
            let whereClause = 'WHERE e.status != \'terminated\'';
            const params = [];
            let paramIndex = 1;
            if (filters.search) {
                const searchEncryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
                params.push(searchEncryptionKey);
                const searchKeyParamIndex = paramIndex;
                paramIndex++;
                whereClause += ` AND (pgp_sym_decrypt(e.first_name_encrypted, $${searchKeyParamIndex}) ILIKE $${paramIndex} OR pgp_sym_decrypt(e.last_name_encrypted, $${searchKeyParamIndex}) ILIKE $${paramIndex} OR e.email ILIKE $${paramIndex})`;
                params.push(`%${filters.search}%`);
                paramIndex++;
            }
            if (filters.department) {
                whereClause += ` AND e.department_id = $${paramIndex}`;
                params.push(filters.department);
                paramIndex++;
            }
            if (filters.status) {
                whereClause += ` AND e.status = $${paramIndex}`;
                params.push(filters.status);
                paramIndex++;
            }
            if (filters.manager) {
                whereClause += ` AND e.manager_id = $${paramIndex}`;
                params.push(filters.manager);
                paramIndex++;
            }
            if (!user.permissions.includes('hr') && !user.permissions.includes('hr_admin')) {
                if (user.permissions.includes('manager') && user.departmentId) {
                    whereClause += ` AND e.department_id = $${paramIndex}`;
                    params.push(user.departmentId);
                    paramIndex++;
                }
                else {
                    whereClause += ` AND e.id = $${paramIndex}`;
                    params.push(user.employeeId);
                    paramIndex++;
                }
            }
            const sortBy = filters.sortBy || 'first_name';
            const sortOrder = filters.sortOrder === 'desc' ? 'DESC' : 'ASC';
            const sortFieldMap = {
                'firstName': 'first_name',
                'lastName': 'last_name',
                'email': 'email',
                'hireDate': 'hire_date',
                'department': 'department_name'
            };
            const dbSortField = sortFieldMap[sortBy] || sortBy;
            let orderClause = '';
            if (dbSortField === 'first_name' || dbSortField === 'last_name') {
                const sortEncryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
                params.push(sortEncryptionKey);
                const sortKeyParamIndex = paramIndex;
                paramIndex++;
                orderClause = `ORDER BY pgp_sym_decrypt(e.${dbSortField}_encrypted, $${sortKeyParamIndex}) ${sortOrder}`;
            }
            else if (dbSortField === 'department_name') {
                orderClause = `ORDER BY d.name ${sortOrder}`;
            }
            else {
                orderClause = `ORDER BY e.${dbSortField} ${sortOrder}`;
            }
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            params.push(encryptionKey);
            const encryptionParamIndex = paramIndex;
            paramIndex++;
            const query = `
        SELECT
          e.*,
          d.name as department_name,
          p.title as position_title,
          CASE
            WHEN m.id IS NOT NULL THEN
              pgp_sym_decrypt(m.first_name_encrypted, $${encryptionParamIndex})
            ELSE NULL
          END as manager_first_name,
          CASE
            WHEN m.id IS NOT NULL THEN
              pgp_sym_decrypt(m.last_name_encrypted, $${encryptionParamIndex})
            ELSE NULL
          END as manager_last_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employees m ON e.manager_id = m.id
        ${whereClause}
        ${orderClause}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM employees e
        ${whereClause}
      `;
            const [dataResult, countResult] = await Promise.all([
                this.db.query(query, [...params, limit, offset]),
                this.db.query(countQuery, [])
            ]);
            const total = parseInt(countResult.rows[0].total);
            return {
                data: dataResult.rows,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
        }
        catch (error) {
            logger_1.logger.error('Error fetching employees:', error);
            throw new Error('Failed to fetch employees');
        }
    }
    async getEmployeeById(employeeId, user) {
        try {
            const query = `
        SELECT
          e.*,
          d.name as department_name,
          p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.id = $1 AND e.status != 'terminated'
      `;
            const result = await this.db.query(query, [employeeId]);
            if (result.rows.length === 0) {
                return null;
            }
            const rawEmployee = result.rows[0];
            if (!this.hasEmployeeAccess(rawEmployee, user)) {
                return null;
            }
            const employee = await this.decryptEmployeeData(rawEmployee);
            return {
                ...employee,
                departmentName: rawEmployee.department_name,
                positionTitle: rawEmployee.position_title
            };
        }
        catch (error) {
            logger_1.logger.error('Error fetching employee:', error);
            throw new Error('Failed to fetch employee');
        }
    }
    async createEmployee(employeeData, createdBy) {
        try {
            const employeeId = (0, uuid_1.v4)();
            const employeeIdResult = await this.db.query('SELECT COUNT(*) as count FROM employees');
            const employeeCount = parseInt(employeeIdResult.rows[0].count) + 1;
            const humanReadableId = `EMP${employeeCount.toString().padStart(4, '0')}`;
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default_key_change_in_production';
            const query = `
        INSERT INTO employees (
          id, employee_id, email, personal_email,
          first_name_encrypted, last_name_encrypted, phone_encrypted,
          date_of_birth_encrypted, salary_encrypted,
          gender, department_id, position_id, manager_id,
          employment_type, hire_date, currency, work_location,
          skills, certifications, status, created_by, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4,
          pgp_sym_encrypt($5, $6), pgp_sym_encrypt($7, $6), pgp_sym_encrypt($8, $6),
          pgp_sym_encrypt($9, $6), pgp_sym_encrypt($10, $6),
          $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, 'active', $21, NOW(), NOW()
        ) RETURNING id, employee_id, email, personal_email, gender, department_id,
                   position_id, manager_id, employment_type, hire_date, currency,
                   work_location, skills, certifications, status, created_at, updated_at
      `;
            const result = await this.db.query(query, [
                employeeId,
                humanReadableId,
                employeeData.email,
                employeeData.personalEmail,
                employeeData.firstName,
                encryptionKey,
                employeeData.lastName,
                employeeData.phone || '',
                employeeData.dateOfBirth || '',
                employeeData.salary?.toString() || '0',
                employeeData.gender,
                employeeData.departmentId,
                employeeData.positionId,
                employeeData.managerId,
                employeeData.employeeType || 'full_time',
                employeeData.hireDate,
                employeeData.currency || 'USD',
                employeeData.workLocation || 'office',
                JSON.stringify(employeeData.skills || []),
                JSON.stringify(employeeData.certifications || []),
                createdBy
            ]);
            logger_1.logger.info('Employee created', {
                employeeId,
                humanReadableId,
                email: employeeData.email,
                createdBy
            });
            const rawEmployee = result.rows[0];
            const employee = await this.decryptEmployeeData(rawEmployee);
            return {
                ...employee,
                employeeId: humanReadableId
            };
        }
        catch (error) {
            logger_1.logger.error('Error creating employee:', error);
            throw new Error('Failed to create employee');
        }
    }
    async updateEmployee(employeeId, updateData, user) {
        try {
            const existingEmployee = await this.getEmployeeById(employeeId, user);
            if (!existingEmployee) {
                return null;
            }
            const allowedFields = this.getAllowedUpdateFields(user);
            const filteredData = Object.keys(updateData)
                .filter(key => allowedFields.includes(key))
                .reduce((obj, key) => {
                obj[key] = updateData[key];
                return obj;
            }, {});
            if (Object.keys(filteredData).length === 0) {
                return existingEmployee;
            }
            const setClause = Object.keys(filteredData)
                .map((key, index) => `${this.getColumnName(key)} = $${index + 2}`)
                .join(', ');
            const query = `
        UPDATE employees 
        SET ${setClause}, updated_at = NOW(), updated_by = $1
        WHERE id = $${Object.keys(filteredData).length + 2}
        RETURNING *
      `;
            const params = [user.id, ...Object.values(filteredData), employeeId];
            const result = await this.db.query(query, params);
            logger_1.logger.info('Employee updated', {
                employeeId,
                updatedBy: user.id,
                fields: Object.keys(filteredData)
            });
            return result.rows[0];
        }
        catch (error) {
            logger_1.logger.error('Error updating employee:', error);
            throw new Error('Failed to update employee');
        }
    }
    async deactivateEmployee(employeeId, deactivatedBy) {
        try {
            const query = `
        UPDATE employees 
        SET status = 'inactive', updated_at = NOW(), updated_by = $1
        WHERE id = $2 AND status != 'terminated'
      `;
            const result = await this.db.query(query, [deactivatedBy, employeeId]);
            if (result.rowCount === 0) {
                return false;
            }
            logger_1.logger.info('Employee deactivated', {
                employeeId,
                deactivatedBy
            });
            return true;
        }
        catch (error) {
            logger_1.logger.error('Error deactivating employee:', error);
            throw new Error('Failed to deactivate employee');
        }
    }
    async getEmployeeProfile(employeeId, user) {
        try {
            const employee = await this.getEmployeeById(employeeId, user);
            if (!employee) {
                return null;
            }
            const [skillsResult, reviewsResult, goalsResult] = await Promise.all([
                this.db.query('SELECT * FROM employee_skills WHERE employee_id = $1', [employeeId]),
                this.db.query('SELECT * FROM performance_reviews WHERE employee_id = $1 ORDER BY review_date DESC LIMIT 5', [employeeId]),
                this.db.query('SELECT * FROM employee_goals WHERE employee_id = $1 AND status = \'active\'', [employeeId])
            ]);
            return {
                ...employee,
                skills: skillsResult.rows,
                recentReviews: reviewsResult.rows,
                activeGoals: goalsResult.rows
            };
        }
        catch (error) {
            logger_1.logger.error('Error fetching employee profile:', error);
            throw new Error('Failed to fetch employee profile');
        }
    }
    hasEmployeeAccess(employee, user) {
        if (user.permissions.includes('hr') || user.permissions.includes('hr_admin')) {
            return true;
        }
        if (user.permissions.includes('manager') && employee.department_id === user.departmentId) {
            return true;
        }
        if (employee.id === user.employeeId) {
            return true;
        }
        return false;
    }
    getAllowedUpdateFields(user) {
        const baseFields = ['personalEmail', 'phone', 'address', 'emergencyContact'];
        if (user.permissions.includes('hr_admin')) {
            return [
                ...baseFields,
                'firstName', 'lastName', 'email', 'departmentId', 'positionId',
                'managerId', 'employeeType', 'salary', 'currency', 'workLocation',
                'skills', 'certifications'
            ];
        }
        if (user.permissions.includes('hr')) {
            return [
                ...baseFields,
                'departmentId', 'positionId', 'managerId', 'workLocation', 'skills'
            ];
        }
        return baseFields;
    }
    getColumnName(fieldName) {
        const fieldMap = {
            firstName: 'first_name',
            lastName: 'last_name',
            personalEmail: 'personal_email',
            dateOfBirth: 'date_of_birth',
            emergencyContact: 'emergency_contact',
            departmentId: 'department_id',
            positionId: 'position_id',
            managerId: 'manager_id',
            employeeType: 'employee_type',
            hireDate: 'hire_date',
            workLocation: 'work_location'
        };
        return fieldMap[fieldName] || fieldName;
    }
    async getEmployeeTeam(employeeId, user) {
        try {
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.manager_id = $1 AND e.status = 'active'
        ORDER BY pgp_sym_decrypt(e.first_name_encrypted, $2), pgp_sym_decrypt(e.last_name_encrypted, $2)
      `;
            const result = await this.db.query(query, [employeeId, encryptionKey]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error fetching employee team:', error);
            throw error;
        }
    }
    async getDirectReports(employeeId, user) {
        try {
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.manager_id = $1 AND e.status = 'active'
        ORDER BY pgp_sym_decrypt(e.first_name_encrypted, $2), pgp_sym_decrypt(e.last_name_encrypted, $2)
      `;
            const result = await this.db.query(query, [employeeId, encryptionKey]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error fetching direct reports:', error);
            throw error;
        }
    }
    async addEmployeeSkills(employeeId, skills, user) {
        try {
            const query = `
        UPDATE employees
        SET skills = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING skills
      `;
            const result = await this.db.query(query, [JSON.stringify(skills), employeeId]);
            return result.rows[0];
        }
        catch (error) {
            logger_1.logger.error('Error adding employee skills:', error);
            throw error;
        }
    }
    async getEmployeeAnalytics(filters, user) {
        try {
            const analytics = {
                totalEmployees: 0,
                activeEmployees: 0,
                departmentBreakdown: [],
                averageTenure: 0,
                turnoverRate: 0
            };
            const countQuery = `
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active
        FROM employees
        WHERE status != 'terminated'
      `;
            const countResult = await this.db.query(countQuery);
            analytics.totalEmployees = parseInt(countResult.rows[0].total);
            analytics.activeEmployees = parseInt(countResult.rows[0].active);
            return analytics;
        }
        catch (error) {
            logger_1.logger.error('Error fetching employee analytics:', error);
            throw error;
        }
    }
    async searchEmployees(filters, user) {
        try {
            const searchTerm = filters.q || '';
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.status = 'active'
        AND (
          pgp_sym_decrypt(e.first_name_encrypted, $1) ILIKE $2 OR
          pgp_sym_decrypt(e.last_name_encrypted, $1) ILIKE $2 OR
          e.email ILIKE $2 OR
          e.employee_id ILIKE $2
        )
        ORDER BY pgp_sym_decrypt(e.first_name_encrypted, $1), pgp_sym_decrypt(e.last_name_encrypted, $1)
        LIMIT 20
      `;
            const result = await this.db.query(query, [encryptionKey, `%${searchTerm}%`]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error searching employees:', error);
            throw error;
        }
    }
    async getManagers(user) {
        try {
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            const query = `
        SELECT DISTINCT
          e.id,
          pgp_sym_decrypt(e.first_name_encrypted, $1) as first_name,
          pgp_sym_decrypt(e.last_name_encrypted, $1) as last_name,
          e.department_id,
          d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.status = 'active'
          AND (
            p.level IN ('manager', 'director', 'vp', 'c_level') OR
            p.title ILIKE '%manager%' OR
            p.title ILIKE '%director%' OR
            p.title ILIKE '%lead%' OR
            p.title ILIKE '%head%'
          )
        ORDER BY first_name, last_name
      `;
            const result = await this.db.query(query, [encryptionKey]);
            return result.rows.map(row => ({
                id: row.id,
                name: `${row.first_name} ${row.last_name}`.trim(),
                departmentId: row.department_id
            }));
        }
        catch (error) {
            logger_1.logger.error('Error fetching managers:', error);
            throw error;
        }
    }
}
exports.EmployeeService = EmployeeService;
//# sourceMappingURL=employeeService.js.map