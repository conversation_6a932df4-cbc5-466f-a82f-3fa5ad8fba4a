const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

async function createAwadheshUser() {
  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'peoplenest',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'SecurePeopleNest2025!',
  });
  
  try {
    console.log('🔧 Creating new super admin user: awadhesh');
    
    // Check if user already exists
    const existingUser = await pool.query(
      'SELECT email FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (existingUser.rows.length > 0) {
      console.log('⚠️  User <EMAIL> already exists');
      
      // Update existing user
      const hashedPassword = await bcrypt.hash('awadhesh123', 12);
      
      await pool.query(`
        UPDATE users 
        SET 
          password_hash = $1,
          role = 'super_admin',
          is_active = true,
          failed_login_attempts = 0,
          locked_until = NULL,
          updated_at = NOW()
        WHERE email = $2
      `, [hashedPassword, '<EMAIL>']);
      
      console.log('✅ Updated existing user with new password and super_admin role');
    } else {
      // Create new user
      const userId = uuidv4();
      const hashedPassword = await bcrypt.hash('awadhesh123', 12);
      
      await pool.query(`
        INSERT INTO users (
          id, 
          email, 
          password_hash, 
          role, 
          is_active, 
          failed_login_attempts, 
          locked_until,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      `, [
        userId,
        '<EMAIL>',
        hashedPassword,
        'super_admin',
        true,
        0,
        null
      ]);
      
      console.log('✅ Created <NAME_EMAIL>');
      console.log('   User ID:', userId);
    }
    
    // Verify the user was created/updated correctly
    const verifyUser = await pool.query(`
      SELECT 
        id, 
        email, 
        role, 
        is_active, 
        failed_login_attempts,
        created_at,
        updated_at
      FROM users 
      WHERE email = $1
    `, ['<EMAIL>']);
    
    if (verifyUser.rows.length > 0) {
      const user = verifyUser.rows[0];
      console.log('\n📊 User verification:');
      console.log('   ID:', user.id);
      console.log('   Email:', user.email);
      console.log('   Role:', user.role);
      console.log('   Active:', user.is_active);
      console.log('   Failed attempts:', user.failed_login_attempts);
      console.log('   Created:', user.created_at);
      console.log('   Updated:', user.updated_at);
      
      // Test password verification
      const testPassword = 'awadhesh123';
      const passwordResult = await pool.query(
        'SELECT password_hash FROM users WHERE email = $1',
        ['<EMAIL>']
      );
      
      const isValid = await bcrypt.compare(testPassword, passwordResult.rows[0].password_hash);
      console.log('   Password test:', isValid ? '✅ VALID' : '❌ INVALID');
      
      if (isValid) {
        console.log('\n🎉 User <EMAIL> is ready for login!');
        console.log('   Email: <EMAIL>');
        console.log('   Password: awadhesh123');
        console.log('   Role: super_admin');
      }
    }
    
  } catch (error) {
    console.error('❌ Error creating user:', error);
  } finally {
    await pool.end();
  }
}

createAwadheshUser();
