"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentService = void 0;
const databaseService_1 = require("./databaseService");
const logger_1 = require("../utils/logger");
class DepartmentService {
    constructor() {
        this.db = new databaseService_1.DatabaseService();
    }
    async getAllDepartments(filters, user) {
        try {
            const params = [];
            let paramCount = 0;
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            params.push(encryptionKey);
            paramCount++;
            let query = `
        SELECT
          d.*,
          pd.name as parent_department_name,
          COUNT(e.id) as employee_count,
          CASE
            WHEN emp.id IS NOT NULL THEN
              COALESCE(
                pgp_sym_decrypt(emp.first_name_encrypted, $${paramCount}) || ' ' ||
                pgp_sym_decrypt(emp.last_name_encrypted, $${paramCount}),
                'Unknown'
              )
            ELSE NULL
          END as head_name
        FROM departments d
        LEFT JOIN departments pd ON d.parent_department_id = pd.id
        LEFT JOIN employees e ON e.department_id = d.id AND e.status = 'active'
        LEFT JOIN employees emp ON d.head_of_department = emp.id
        WHERE 1=1
      `;
            if (!filters.includeInactive) {
                query += ` AND d.is_active = true`;
            }
            if (filters.parentId) {
                paramCount++;
                query += ` AND d.parent_department_id = $${paramCount}`;
                params.push(filters.parentId);
            }
            if (filters.search) {
                paramCount++;
                query += ` AND (d.name ILIKE $${paramCount} OR d.code ILIKE $${paramCount} OR d.description ILIKE $${paramCount})`;
                params.push(`%${filters.search}%`);
            }
            query += ` GROUP BY d.id, pd.name, emp.id, emp.first_name_encrypted, emp.last_name_encrypted`;
            const sortBy = filters.sortBy || 'name';
            const sortOrder = filters.sortOrder || 'asc';
            query += ` ORDER BY d.${sortBy} ${sortOrder.toUpperCase()}`;
            const result = await this.db.query(query, params);
            return result.rows.map(row => ({
                id: row.id,
                name: row.name,
                description: row.description,
                code: row.code,
                headOfDepartment: row.head_name,
                parentDepartmentId: row.parent_department_id,
                parentDepartment: row.parent_department_name,
                costCenter: row.cost_center,
                budgetAllocated: row.budget_allocated,
                isActive: row.is_active,
                createdAt: row.created_at,
                updatedAt: row.updated_at,
                employeeCount: parseInt(row.employee_count) || 0
            }));
        }
        catch (error) {
            logger_1.logger.error('Error fetching departments:', error);
            throw new Error('Failed to fetch departments');
        }
    }
    async getDepartmentById(departmentId, user) {
        try {
            const query = `
        SELECT 
          d.*,
          pd.name as parent_department_name,
          COUNT(e.id) as employee_count,
          emp.first_name || ' ' || emp.last_name as head_name
        FROM departments d
        LEFT JOIN departments pd ON d.parent_department_id = pd.id
        LEFT JOIN employees e ON e.department_id = d.id AND e.status = 'active'
        LEFT JOIN employees emp ON d.head_of_department = emp.id
        WHERE d.id = $1
        GROUP BY d.id, pd.name, emp.first_name, emp.last_name
      `;
            const result = await this.db.query(query, [departmentId]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            return {
                id: row.id,
                name: row.name,
                description: row.description,
                code: row.code,
                headOfDepartment: row.head_name,
                parentDepartmentId: row.parent_department_id,
                parentDepartment: row.parent_department_name,
                costCenter: row.cost_center,
                budgetAllocated: row.budget_allocated,
                isActive: row.is_active,
                createdAt: row.created_at,
                updatedAt: row.updated_at,
                employeeCount: parseInt(row.employee_count) || 0
            };
        }
        catch (error) {
            logger_1.logger.error('Error fetching department by ID:', error);
            throw new Error('Failed to fetch department');
        }
    }
    async createDepartment(data, userId) {
        try {
            const existingQuery = `
        SELECT id FROM departments 
        WHERE (name = $1 OR code = $2) AND is_active = true
      `;
            const existing = await this.db.query(existingQuery, [data.name, data.code]);
            if (existing.rows.length > 0) {
                throw new Error('Department with this name or code already exists');
            }
            const query = `
        INSERT INTO departments (
          name, description, code, head_of_department, 
          parent_department_id, cost_center, budget_allocated
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `;
            const values = [
                data.name,
                data.description,
                data.code,
                data.headOfDepartment,
                data.parentDepartmentId,
                data.costCenter,
                data.budgetAllocated
            ];
            const result = await this.db.query(query, values);
            const department = result.rows[0];
            return {
                id: department.id,
                name: department.name,
                description: department.description,
                code: department.code,
                headOfDepartment: department.head_of_department,
                parentDepartmentId: department.parent_department_id,
                costCenter: department.cost_center,
                budgetAllocated: department.budget_allocated,
                isActive: department.is_active,
                createdAt: department.created_at,
                updatedAt: department.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error creating department:', error);
            throw error;
        }
    }
    async updateDepartment(departmentId, data, userId) {
        try {
            const existingDept = await this.getDepartmentById(departmentId, { id: userId });
            if (!existingDept) {
                return null;
            }
            if (data.name || data.code) {
                const conflictQuery = `
          SELECT id FROM departments 
          WHERE (name = $1 OR code = $2) AND id != $3 AND is_active = true
        `;
                const conflict = await this.db.query(conflictQuery, [
                    data.name || existingDept.name,
                    data.code || existingDept.code,
                    departmentId
                ]);
                if (conflict.rows.length > 0) {
                    throw new Error('Department with this name or code already exists');
                }
            }
            const updateFields = [];
            const values = [];
            let paramCount = 0;
            Object.entries(data).forEach(([key, value]) => {
                if (value !== undefined) {
                    paramCount++;
                    updateFields.push(`${key.replace(/([A-Z])/g, '_$1').toLowerCase()} = $${paramCount}`);
                    values.push(value);
                }
            });
            if (updateFields.length === 0) {
                return existingDept;
            }
            paramCount++;
            updateFields.push(`updated_at = $${paramCount}`);
            values.push(new Date());
            paramCount++;
            values.push(departmentId);
            const query = `
        UPDATE departments 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `;
            const result = await this.db.query(query, values);
            const department = result.rows[0];
            return {
                id: department.id,
                name: department.name,
                description: department.description,
                code: department.code,
                headOfDepartment: department.head_of_department,
                parentDepartmentId: department.parent_department_id,
                costCenter: department.cost_center,
                budgetAllocated: department.budget_allocated,
                isActive: department.is_active,
                createdAt: department.created_at,
                updatedAt: department.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error updating department:', error);
            throw error;
        }
    }
    async deactivateDepartment(departmentId, userId) {
        try {
            const employeeCheck = await this.db.query('SELECT COUNT(*) as count FROM employees WHERE department_id = $1 AND status = $2', [departmentId, 'active']);
            if (parseInt(employeeCheck.rows[0].count) > 0) {
                throw new Error('Cannot deactivate department with active employees');
            }
            const query = `
        UPDATE departments 
        SET is_active = false, updated_at = NOW()
        WHERE id = $1 AND is_active = true
        RETURNING id
      `;
            const result = await this.db.query(query, [departmentId]);
            return result.rows.length > 0;
        }
        catch (error) {
            logger_1.logger.error('Error deactivating department:', error);
            throw error;
        }
    }
    async getDepartmentEmployees(departmentId, filters, user) {
        try {
            let query = `
        SELECT e.*, p.title as position_title
        FROM employees e
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.department_id = $1
      `;
            const params = [departmentId];
            let paramCount = 1;
            if (filters.status) {
                paramCount++;
                query += ` AND e.status = $${paramCount}`;
                params.push(filters.status);
            }
            else {
                query += ` AND e.status = 'active'`;
            }
            if (filters.includeSubdepartments) {
                query = `
          WITH RECURSIVE dept_hierarchy AS (
            SELECT id FROM departments WHERE id = $1
            UNION ALL
            SELECT d.id FROM departments d
            INNER JOIN dept_hierarchy dh ON d.parent_department_id = dh.id
          )
          SELECT e.*, p.title as position_title
          FROM employees e
          LEFT JOIN positions p ON e.position_id = p.id
          WHERE e.department_id IN (SELECT id FROM dept_hierarchy)
        `;
                if (filters.status) {
                    paramCount++;
                    query += ` AND e.status = $${paramCount}`;
                    params.push(filters.status);
                }
                else {
                    query += ` AND e.status = 'active'`;
                }
            }
            const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';
            params.push(encryptionKey);
            paramCount++;
            query += ` ORDER BY pgp_sym_decrypt(e.first_name_encrypted, $${paramCount}), pgp_sym_decrypt(e.last_name_encrypted, $${paramCount})`;
            const result = await this.db.query(query, params);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error fetching department employees:', error);
            throw new Error('Failed to fetch department employees');
        }
    }
    async getDepartmentHierarchy(departmentId, user) {
        try {
            const query = `
        WITH RECURSIVE 
        parent_hierarchy AS (
          SELECT id, name, parent_department_id, 0 as level
          FROM departments WHERE id = $1
          UNION ALL
          SELECT d.id, d.name, d.parent_department_id, ph.level - 1
          FROM departments d
          INNER JOIN parent_hierarchy ph ON d.id = ph.parent_department_id
        ),
        child_hierarchy AS (
          SELECT id, name, parent_department_id, 0 as level
          FROM departments WHERE id = $1
          UNION ALL
          SELECT d.id, d.name, d.parent_department_id, ch.level + 1
          FROM departments d
          INNER JOIN child_hierarchy ch ON d.parent_department_id = ch.id
        )
        SELECT * FROM parent_hierarchy
        UNION
        SELECT * FROM child_hierarchy
        WHERE level != 0
        ORDER BY level, name
      `;
            const result = await this.db.query(query, [departmentId]);
            return result.rows;
        }
        catch (error) {
            logger_1.logger.error('Error fetching department hierarchy:', error);
            throw new Error('Failed to fetch department hierarchy');
        }
    }
    async getDepartmentAnalytics(filters, user) {
        try {
            const query = `
        SELECT 
          COUNT(*) as total_departments,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_departments,
          SUM(budget_allocated) as total_budget,
          AVG(budget_allocated) as avg_budget,
          (SELECT COUNT(*) FROM employees WHERE status = 'active') as total_employees
        FROM departments
      `;
            const result = await this.db.query(query);
            return result.rows[0];
        }
        catch (error) {
            logger_1.logger.error('Error fetching department analytics:', error);
            throw new Error('Failed to fetch department analytics');
        }
    }
}
exports.DepartmentService = DepartmentService;
//# sourceMappingURL=departmentService.js.map