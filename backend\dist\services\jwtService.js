"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWTService = exports.jwtService = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const logger_1 = require("../utils/logger");
const databaseService_1 = require("./databaseService");
const db = new databaseService_1.DatabaseService();
class JWTService {
    constructor() {
        this.accessTokenSecret = process.env.JWT_ACCESS_SECRET || 'your-access-secret-key';
        this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
        this.accessTokenExpiry = process.env.JWT_ACCESS_EXPIRY || '15m';
        this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d';
        this.issuer = process.env.JWT_ISSUER || 'peoplenest-api';
        this.audience = process.env.JWT_AUDIENCE || 'peoplenest-client';
        this.algorithm = 'HS256';
        if (process.env.NODE_ENV === 'production') {
            this.validateProductionConfig();
        }
    }
    async generateTokenPair(userId, email, role, permissions, sessionId, deviceFingerprint, ipAddress, userAgent) {
        try {
            const jti = crypto_1.default.randomUUID();
            const accessPayload = {
                userId,
                email,
                role,
                permissions,
                sessionId,
                deviceFingerprint,
                jti,
                iss: this.issuer,
                aud: this.audience
            };
            const accessToken = jsonwebtoken_1.default.sign(accessPayload, this.accessTokenSecret, {
                expiresIn: this.accessTokenExpiry,
                algorithm: this.algorithm
            });
            const refreshTokenId = crypto_1.default.randomUUID();
            const refreshToken = jsonwebtoken_1.default.sign({
                userId,
                sessionId,
                tokenId: refreshTokenId,
                type: 'refresh',
                iss: this.issuer,
                aud: this.audience
            }, this.refreshTokenSecret, {
                expiresIn: this.refreshTokenExpiry,
                algorithm: this.algorithm
            });
            await this.storeRefreshToken({
                id: refreshTokenId,
                userId,
                sessionId,
                deviceFingerprint,
                ipAddress,
                userAgent,
                expiresAt: new Date(Date.now() + this.parseExpiry(this.refreshTokenExpiry)),
                isRevoked: false,
                createdAt: new Date()
            });
            const expiresIn = this.parseExpiry(this.accessTokenExpiry) / 1000;
            logger_1.logger.info('Token pair generated', {
                userId,
                sessionId,
                deviceFingerprint: deviceFingerprint.substring(0, 8) + '...',
                expiresIn
            });
            return {
                accessToken,
                refreshToken,
                expiresIn,
                tokenType: 'Bearer'
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to generate token pair:', error);
            throw new Error('Token generation failed');
        }
    }
    async verifyAccessToken(token) {
        try {
            if (JWTService.tokenBlacklist.has(token)) {
                throw new Error('Token has been revoked');
            }
            const decoded = jsonwebtoken_1.default.verify(token, this.accessTokenSecret, {
                algorithms: [this.algorithm],
                issuer: this.issuer,
                audience: this.audience
            });
            if (!decoded.userId || !decoded.sessionId) {
                throw new Error('Invalid token payload');
            }
            const isSessionValid = await this.validateSession(decoded.sessionId);
            if (!isSessionValid) {
                throw new Error('Session has expired or been invalidated');
            }
            return decoded;
        }
        catch (error) {
            logger_1.logger.warn('Access token verification failed:', {
                error: error.message,
                token: token.substring(0, 20) + '...'
            });
            throw error;
        }
    }
    async refreshAccessToken(refreshToken, deviceFingerprint) {
        try {
            const decoded = jsonwebtoken_1.default.verify(refreshToken, this.refreshTokenSecret, {
                algorithms: [this.algorithm],
                issuer: this.issuer,
                audience: this.audience
            });
            if (decoded.type !== 'refresh') {
                throw new Error('Invalid token type');
            }
            const storedToken = await this.getRefreshToken(decoded.tokenId);
            if (!storedToken || storedToken.isRevoked) {
                throw new Error('Refresh token has been revoked');
            }
            if (storedToken.deviceFingerprint !== deviceFingerprint) {
                logger_1.logger.warn('Device fingerprint mismatch during token refresh', {
                    userId: storedToken.userId,
                    sessionId: storedToken.sessionId,
                    storedFingerprint: storedToken.deviceFingerprint.substring(0, 8) + '...',
                    providedFingerprint: deviceFingerprint.substring(0, 8) + '...'
                });
                throw new Error('Device fingerprint mismatch');
            }
            if (storedToken.expiresAt < new Date()) {
                await this.revokeRefreshToken(decoded.tokenId);
                throw new Error('Refresh token has expired');
            }
            const user = await this.getUserData(storedToken.userId);
            if (!user) {
                throw new Error('User not found');
            }
            const newTokenPair = await this.generateTokenPair(user.id, user.email, user.role, user.permissions, storedToken.sessionId, deviceFingerprint, storedToken.ipAddress, storedToken.userAgent);
            await this.revokeRefreshToken(decoded.tokenId);
            logger_1.logger.info('Access token refreshed', {
                userId: user.id,
                sessionId: storedToken.sessionId
            });
            return newTokenPair;
        }
        catch (error) {
            logger_1.logger.warn('Token refresh failed:', error.message);
            throw error;
        }
    }
    async revokeAccessToken(token) {
        try {
            JWTService.tokenBlacklist.add(token);
            logger_1.logger.info('Access token revoked');
        }
        catch (error) {
            logger_1.logger.error('Failed to revoke access token:', error);
        }
    }
    async revokeRefreshToken(tokenId) {
        try {
            await db.query('UPDATE user_sessions SET refresh_token_revoked = true WHERE refresh_token_id = $1', [tokenId]);
            logger_1.logger.info('Refresh token revoked', { tokenId });
        }
        catch (error) {
            logger_1.logger.error('Failed to revoke refresh token:', error);
        }
    }
    async revokeAllTokens(sessionId) {
        try {
            await db.query('UPDATE user_sessions SET refresh_token_revoked = true WHERE session_id = $1', [sessionId]);
            logger_1.logger.info('All tokens revoked for session', { sessionId });
        }
        catch (error) {
            logger_1.logger.error('Failed to revoke all tokens:', error);
        }
    }
    async storeRefreshToken(tokenData) {
        const query = `
      INSERT INTO user_sessions (
        user_id, session_id, refresh_token,
        ip_address, user_agent, expires_at, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (session_id)
      DO UPDATE SET
        refresh_token = $3,
        ip_address = $4,
        user_agent = $5,
        expires_at = $6,
        last_activity = NOW()
    `;
        await db.query(query, [
            tokenData.userId,
            tokenData.sessionId,
            tokenData.id,
            tokenData.ipAddress,
            tokenData.userAgent,
            tokenData.expiresAt,
            tokenData.createdAt
        ]);
    }
    async getRefreshToken(tokenId) {
        const result = await db.query('SELECT * FROM user_sessions WHERE refresh_token_id = $1', [tokenId]);
        if (result.rows.length === 0) {
            return null;
        }
        const row = result.rows[0];
        return {
            id: row.refresh_token_id,
            userId: row.user_id,
            sessionId: row.session_id,
            deviceFingerprint: row.device_fingerprint,
            ipAddress: row.ip_address,
            userAgent: row.user_agent,
            expiresAt: row.expires_at,
            isRevoked: row.refresh_token_revoked,
            createdAt: row.created_at
        };
    }
    async validateSession(sessionId) {
        const result = await db.query('SELECT 1 FROM user_sessions WHERE session_id = $1 AND refresh_token_revoked = false', [sessionId]);
        return result.rows.length > 0;
    }
    async getUserData(userId) {
        const result = await db.query('SELECT id, email, role FROM users WHERE id = $1 AND is_active = true', [userId]);
        if (result.rows.length === 0) {
            return null;
        }
        const user = result.rows[0];
        const permissions = await this.getUserPermissions(user.role);
        return {
            ...user,
            permissions
        };
    }
    async getUserPermissions(role) {
        const rolePermissions = {
            'super_admin': [
                'super_admin', 'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
            ],
            'hr_admin': [
                'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
            ],
            'hr': [
                'hr', 'employee', 'recruiter'
            ],
            'manager': [
                'manager', 'employee'
            ],
            'employee': [
                'employee'
            ]
        };
        return rolePermissions[role] || ['employee'];
    }
    parseExpiry(expiry) {
        const unit = expiry.slice(-1);
        const value = parseInt(expiry.slice(0, -1));
        switch (unit) {
            case 's': return value * 1000;
            case 'm': return value * 60 * 1000;
            case 'h': return value * 60 * 60 * 1000;
            case 'd': return value * 24 * 60 * 60 * 1000;
            default: return 15 * 60 * 1000;
        }
    }
    validateProductionConfig() {
        const requiredEnvVars = [
            'JWT_ACCESS_SECRET',
            'JWT_REFRESH_SECRET',
            'JWT_ISSUER',
            'JWT_AUDIENCE'
        ];
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                throw new Error(`Missing required environment variable: ${envVar}`);
            }
        }
        if (this.accessTokenSecret.length < 32) {
            throw new Error('JWT_ACCESS_SECRET must be at least 32 characters long');
        }
        if (this.refreshTokenSecret.length < 32) {
            throw new Error('JWT_REFRESH_SECRET must be at least 32 characters long');
        }
    }
    static generateDeviceFingerprint(userAgent, acceptLanguage = '', acceptEncoding = '') {
        const data = `${userAgent}|${acceptLanguage}|${acceptEncoding}`;
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    async cleanupExpiredTokens() {
        try {
            const result = await db.query('DELETE FROM user_sessions WHERE expires_at < NOW() OR refresh_token_revoked = true');
            logger_1.logger.info(`Cleaned up ${result.rowCount} expired/revoked tokens`);
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup expired tokens:', error);
        }
    }
}
exports.JWTService = JWTService;
JWTService.tokenBlacklist = new Set();
exports.jwtService = new JWTService();
exports.default = exports.jwtService;
//# sourceMappingURL=jwtService.js.map