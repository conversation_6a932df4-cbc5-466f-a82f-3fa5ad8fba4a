"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/employees/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/employees/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/employees/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmployeesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Calendar,CheckSquare,Clock,DollarSign,Download,Edit,Eye,FileText,Filter,Grid3X3,List,Loader2,Mail,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Send,SortAsc,SortDesc,Star,Trash2,TrendingUp,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/touch-friendly */ \"(app-pages-browser)/./src/components/ui/touch-friendly.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_employees_EmployeeForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/employees/EmployeeForm */ \"(app-pages-browser)/./src/components/employees/EmployeeForm.tsx\");\n/* harmony import */ var _components_employees_EmployeeProfile__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/employees/EmployeeProfile */ \"(app-pages-browser)/./src/components/employees/EmployeeProfile.tsx\");\n/* harmony import */ var _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/employeeApi */ \"(app-pages-browser)/./src/lib/api/employeeApi.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Note: All employee data now comes from the database via API calls\n// Mock data has been removed and replaced with real database integration\n// Filter options for UI dropdowns\nconst statuses = [\n    \"All\",\n    \"active\",\n    \"on-leave\",\n    \"inactive\"\n];\nfunction EmployeesPage() {\n    _s();\n    // Data state\n    const [employees, setEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [positions, setPositions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [managers, setManagers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalEmployees, setTotalEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Filter and search state\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"firstName\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    const [selectedEmployees, setSelectedEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAdvancedFilters, setShowAdvancedFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [performanceFilter, setPerformanceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [salaryRange, setSalaryRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        200000\n    ]);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBulkActions, setShowBulkActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal state\n    const [showEmployeeForm, setShowEmployeeForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEmployeeProfile, setShowEmployeeProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEmployeeId, setSelectedEmployeeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEmployee, setEditingEmployee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmployeesPage.useEffect\": ()=>{\n            loadInitialData();\n        }\n    }[\"EmployeesPage.useEffect\"], []);\n    // Load employees when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmployeesPage.useEffect\": ()=>{\n            loadEmployees();\n        }\n    }[\"EmployeesPage.useEffect\"], [\n        searchQuery,\n        selectedDepartment,\n        selectedStatus,\n        sortBy,\n        sortOrder,\n        currentPage\n    ]);\n    const loadInitialData = async ()=>{\n        setLoading(true);\n        try {\n            const [deptResponse, posResponse, mgrResponse] = await Promise.all([\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getDepartments(),\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getPositions(),\n                _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getManagers()\n            ]);\n            const deptData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(deptResponse);\n            const posData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(posResponse);\n            const mgrData = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(mgrResponse);\n            if (deptData) setDepartments([\n                {\n                    id: \"All\",\n                    name: \"All Departments\"\n                },\n                ...deptData\n            ]);\n            if (posData) setPositions(posData);\n            if (mgrData) setManagers(mgrData);\n            await loadEmployees();\n        } catch (error) {\n            console.error(\"Failed to load initial data:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load employee data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadEmployees = async ()=>{\n        const filters = {\n            page: currentPage,\n            limit: 20,\n            search: searchQuery || undefined,\n            department: selectedDepartment !== \"All\" ? selectedDepartment : undefined,\n            status: selectedStatus !== \"All\" ? selectedStatus.toLowerCase() : undefined,\n            sortBy: sortBy,\n            sortOrder: sortOrder\n        };\n        const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.getEmployees(filters);\n        const data = (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response);\n        if (data) {\n            setEmployees(data.employees);\n            setTotalEmployees(data.total);\n            setTotalPages(data.totalPages);\n        }\n    };\n    // Note: Filtering and sorting is now handled by the backend API\n    // The employees array already contains the filtered and sorted results\n    const filteredAndSortedEmployees = employees;\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"success\";\n            case \"on-leave\":\n                return \"warning\";\n            case \"inactive\":\n                return \"destructive\";\n            default:\n                return \"secondary\";\n        }\n    };\n    const getPerformanceColor = (rating)=>{\n        if (rating >= 4.5) return \"text-green-600 dark:text-green-400\";\n        if (rating >= 4.0) return \"text-blue-600 dark:text-blue-400\";\n        if (rating >= 3.5) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    const handleSelectAll = ()=>{\n        if (selectedEmployees.length === filteredAndSortedEmployees.length) {\n            setSelectedEmployees([]);\n        } else {\n            setSelectedEmployees(filteredAndSortedEmployees.map((emp)=>emp.id));\n        }\n    };\n    const handleSelectEmployee = (id)=>{\n        setSelectedEmployees((prev)=>prev.includes(id) ? prev.filter((empId)=>empId !== id) : [\n                ...prev,\n                id\n            ]);\n    };\n    const handleViewEmployee = (employeeId)=>{\n        setSelectedEmployeeId(employeeId);\n        setShowEmployeeProfile(true);\n    };\n    const handleEditEmployee = (employee)=>{\n        setEditingEmployee(employee);\n        setShowEmployeeForm(true);\n    };\n    const handleAddEmployee = ()=>{\n        setEditingEmployee(null);\n        setShowEmployeeForm(true);\n    };\n    const handleDeleteEmployee = async (employeeId)=>{\n        if (confirm(\"Are you sure you want to delete this employee?\")) {\n            const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.deleteEmployee(employeeId);\n            if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee deleted successfully\")) {\n                await loadEmployees();\n            }\n        }\n    };\n    const handleBulkAction = async (action)=>{\n        try {\n            switch(action){\n                case 'delete':\n                    if (confirm(\"Are you sure you want to delete \".concat(selectedEmployees.length, \" employees?\"))) {\n                        // Implement bulk delete\n                        sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"\".concat(selectedEmployees.length, \" employees deleted\"));\n                    }\n                    break;\n                case 'export':\n                    const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.exportEmployees({\n                        search: selectedEmployees.join(',')\n                    });\n                    if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Export started\")) {\n                    // Handle export\n                    }\n                    break;\n                default:\n                    console.log(\"Performing \".concat(action, \" on employees:\"), selectedEmployees);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Bulk action failed\");\n        } finally{\n            setSelectedEmployees([]);\n            setShowBulkActions(false);\n        }\n    };\n    const toggleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortBy(field);\n            setSortOrder(\"asc\");\n        }\n    };\n    const handleRefresh = async ()=>{\n        await (0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.withLoading)(loadEmployees, setIsRefreshing);\n    };\n    const handleSwipeLeft = (employeeId)=>{\n        handleDeleteEmployee(employeeId);\n    };\n    const handleSwipeRight = (employeeId)=>{\n        // Implement archive functionality\n        sonner__WEBPACK_IMPORTED_MODULE_11__.toast.info(\"Archive functionality coming soon\");\n    };\n    const handleEmployeeFormSubmit = async (data)=>{\n        try {\n            if (editingEmployee) {\n                const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.updateEmployee(editingEmployee.id, data);\n                if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee updated successfully\")) {\n                    setShowEmployeeForm(false);\n                    setEditingEmployee(null);\n                    await loadEmployees();\n                }\n            } else {\n                const response = await _lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.employeeApi.createEmployee(data);\n                if ((0,_lib_api_employeeApi__WEBPACK_IMPORTED_MODULE_10__.handleApiResponse)(response, \"Employee created successfully\")) {\n                    setShowEmployeeForm(false);\n                    await loadEmployees();\n                }\n            }\n        } catch (error) {\n            console.error(\"Form submission error:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to save employee\");\n        }\n    };\n    const handleCloseForm = ()=>{\n        setShowEmployeeForm(false);\n        setEditingEmployee(null);\n    };\n    const handleCloseProfile = ()=>{\n        setShowEmployeeProfile(false);\n        setSelectedEmployeeId(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.PullToRefresh, {\n        onRefresh: handleRefresh,\n        className: \"flex-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_13__.Header, {\n                    title: \"Employee Management\",\n                    subtitle: \"Managing \".concat(totalEmployees, \" employees across \").concat(departments.length - 1, \" departments\"),\n                    actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            selectedEmployees.length,\n                                            \" selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowBulkActions(!showBulkActions),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            \"Bulk Actions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.TouchButton, {\n                                variant: \"secondary\",\n                                size: \"sm\",\n                                onClick: handleRefresh,\n                                disabled: isRefreshing,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2 \".concat(isRefreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    isRefreshing ? 'Refreshing...' : 'Refresh'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                onClick: handleAddEmployee,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    \"Add Employee\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 7\n                }, this),\n                showBulkActions && selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-blue-900\",\n                                        children: [\n                                            \"Bulk Actions for \",\n                                            selectedEmployees.length,\n                                            \" employees:\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('send-email'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Send Email\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('export-data'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export Data\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleBulkAction('update-status'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Update Status\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setShowBulkActions(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    placeholder: \"Search employees by name, email, position, or department...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedDepartment,\n                                                onChange: (e)=>setSelectedDepartment(e.target.value),\n                                                className: \"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                \"aria-label\": \"Filter by department\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"All\",\n                                                        children: \"All Departments\"\n                                                    }, \"all\", false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: dept.name,\n                                                            children: [\n                                                                dept.name,\n                                                                \" Department\"\n                                                            ]\n                                                        }, dept.id, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                className: \"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                \"aria-label\": \"Filter by status\",\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === \"All\" ? \"All Status\" : status.charAt(0).toUpperCase() + status.slice(1)\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: ()=>setShowAdvancedFilters(!showAdvancedFilters),\n                                                className: showAdvancedFilters ? \"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800\" : \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: ()=>toggleSort(sortBy),\n                                                children: sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 40\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 74\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            showAdvancedFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: \"auto\"\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Sort By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    \"aria-label\": \"Sort by field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"department\",\n                                                            children: \"Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"performance\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"startDate\",\n                                                            children: \"Start Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Performance Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: performanceFilter,\n                                                    onChange: (e)=>setPerformanceFilter(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    \"aria-label\": \"Filter by performance level\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: \"All Performance Levels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"high\",\n                                                            children: \"High (4.5+)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"medium\",\n                                                            children: \"Medium (3.5-4.4)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"low\",\n                                                            children: \"Low (<3.5)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-foreground mb-2\",\n                                                    children: \"Salary Range\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            type: \"number\",\n                                                            placeholder: \"Min\",\n                                                            value: salaryRange[0],\n                                                            onChange: (e)=>setSalaryRange([\n                                                                    parseInt(e.target.value) || 0,\n                                                                    salaryRange[1]\n                                                                ]),\n                                                            className: \"w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            type: \"number\",\n                                                            placeholder: \"Max\",\n                                                            value: salaryRange[1],\n                                                            onChange: (e)=>setSalaryRange([\n                                                                    salaryRange[0],\n                                                                    parseInt(e.target.value) || 200000\n                                                                ]),\n                                                            className: \"w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Employees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.filter((emp)=>emp.status === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-8 w-8 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: employees.filter((emp)=>emp.status === \"on-leave\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"On Leave\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600 dark:text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-foreground\",\n                                                    children: (employees.reduce((sum, emp)=>sum + emp.performance, 0) / employees.length).toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Avg Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredAndSortedEmployees.length,\n                                        \" of \",\n                                        employees.length,\n                                        \" employees\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 11\n                                }, this),\n                                selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                selectedEmployees.length,\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedEmployees([]),\n                                            className: \"text-blue-600 hover:text-blue-700\",\n                                            children: \"Clear selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: viewMode === \"grid\" ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewMode(\"grid\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"Grid\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: viewMode === \"list\" ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewMode(\"list\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"List\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 7\n                }, this),\n                viewMode === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: filteredAndSortedEmployees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.SwipeableCard, {\n                                onSwipeLeft: ()=>handleSwipeLeft(employee.id),\n                                onSwipeRight: ()=>handleSwipeRight(employee.id),\n                                className: \"hover:shadow-lg transition-all cursor-pointer \".concat(selectedEmployees.includes(employee.id) ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20' : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"border-0 shadow-none bg-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: selectedEmployees.includes(employee.id),\n                                                                        onChange: ()=>handleSelectEmployee(employee.id),\n                                                                        className: \"absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                                        \"aria-label\": \"Select \".concat(employee.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                        className: \"h-12 w-12 ml-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                                src: employee.avatar,\n                                                                                alt: employee.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                                children: employee.name.split(' ').map((n)=>n[0]).join('')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-foreground\",\n                                                                        children: employee.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: employee.position\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: getStatusColor(employee.status),\n                                                            children: employee.status.replace('-', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-4 w-4 \".concat(getPerformanceColor(employee.performance))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium \".concat(getPerformanceColor(employee.performance)),\n                                                                    children: employee.performance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"truncate\",\n                                                                    children: employee.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: employee.department\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: employee.location\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Started \",\n                                                                        new Date(employee.startDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        employee.salary.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 15\n                            }, this)\n                        }, employee.id, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this),\n                viewMode === \"list\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-muted/50 border-b border-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectedEmployees.length === filteredAndSortedEmployees.length && filteredAndSortedEmployees.length > 0,\n                                                        onChange: handleSelectAll,\n                                                        className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                        \"aria-label\": \"Select all employees\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"name\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Employee\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"name\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 70\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 104\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Position\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"department\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"department\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 76\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 110\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\",\n                                                    onClick: ()=>toggleSort(\"performance\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Performance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            sortBy === \"performance\" && (sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 77\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 111\n                                                            }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Salary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-6 font-medium text-foreground\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: filteredAndSortedEmployees.map((employee, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.tr, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: index * 0.05\n                                                },\n                                                className: \"border-b border-border hover:bg-muted/50 \".concat(selectedEmployees.includes(employee.id) ? 'bg-blue-50 dark:bg-blue-950/20' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedEmployees.includes(employee.id),\n                                                            onChange: ()=>handleSelectEmployee(employee.id),\n                                                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\",\n                                                            \"aria-label\": \"Select \".concat(employee.name)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                    className: \"h-10 w-10\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                            src: employee.avatar,\n                                                                            alt: employee.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                            children: employee.name.split(' ').map((n)=>n[0]).join('')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-foreground\",\n                                                                            children: employee.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: employee.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-foreground\",\n                                                        children: employee.position\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-muted-foreground\",\n                                                        children: employee.department\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: getStatusColor(employee.status),\n                                                            children: employee.status.replace('-', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-4 w-4 \".concat(getPerformanceColor(employee.performance))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(getPerformanceColor(employee.performance)),\n                                                                    children: employee.performance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6 text-foreground font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            employee.salary.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-8 w-8 text-red-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, employee.id, true, {\n                                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_touch_friendly__WEBPACK_IMPORTED_MODULE_6__.FloatingActionButton, {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 822,\n                        columnNumber: 15\n                    }, void 0),\n                    onClick: handleAddEmployee,\n                    position: \"bottom-right\",\n                    className: \"lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                    open: showEmployeeForm,\n                    onOpenChange: setShowEmployeeForm,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                        className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                    children: editingEmployee ? \"Edit Employee\" : \"Add New Employee\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employees_EmployeeForm__WEBPACK_IMPORTED_MODULE_8__.EmployeeForm, {\n                                employee: editingEmployee,\n                                onSubmit: handleEmployeeFormSubmit,\n                                onCancel: handleCloseForm,\n                                departments: departments.filter((d)=>d.id !== \"All\"),\n                                positions: positions,\n                                managers: managers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                    open: showEmployeeProfile,\n                    onOpenChange: setShowEmployeeProfile,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                        className: \"max-w-6xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                    children: \"Employee Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 11\n                            }, this),\n                            selectedEmployeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employees_EmployeeProfile__WEBPACK_IMPORTED_MODULE_9__.EmployeeProfile, {\n                                employeeId: selectedEmployeeId,\n                                onEdit: ()=>{\n                                    const employee = employees.find((e)=>e.id === selectedEmployeeId);\n                                    if (employee) {\n                                        setShowEmployeeProfile(false);\n                                        handleEditEmployee(employee);\n                                    }\n                                },\n                                onClose: handleCloseProfile\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 7\n                }, this),\n                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Calendar_CheckSquare_Clock_DollarSign_Download_Edit_Eye_FileText_Filter_Grid3X3_List_Loader2_Mail_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Send_SortAsc_SortDesc_Star_Trash2_TrendingUp_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                className: \"h-6 w-6 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Loading employees...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n            lineNumber: 290,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\employees\\\\page.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n}\n_s(EmployeesPage, \"u9PT6KCP1KjFfDnh2VpxQXPmpFY=\");\n_c = EmployeesPage;\nvar _c;\n$RefreshReg$(_c, \"EmployeesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/employees/page.tsx\n"));

/***/ })

});