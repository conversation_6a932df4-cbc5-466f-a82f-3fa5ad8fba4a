"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireSelfOrManagerAccess = exports.requireDepartmentAccess = exports.requirePermission = exports.requireRole = exports.optionalAuthMiddleware = exports.authMiddleware = void 0;
const databaseService_1 = require("../services/databaseService");
const logger_1 = require("../utils/logger");
const jwtService_1 = require("../services/jwtService");
const auditLogger_1 = require("../utils/auditLogger");
const db = new databaseService_1.DatabaseService();
const authMiddleware = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'No valid authorization token provided',
                code: 'MISSING_TOKEN'
            });
        }
        const token = authHeader.substring(7);
        let tokenPayload;
        try {
            tokenPayload = await jwtService_1.jwtService.verifyAccessToken(token);
        }
        catch (error) {
            logger_1.logger.warn('Token verification failed', {
                error: error.message,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path
            });
            await auditLogger_1.auditLogger.logAuthEvent('LOGIN_FAILED', 'unknown', 'unknown', req, { reason: 'invalid_token', error: error.message });
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid or expired token',
                code: 'INVALID_TOKEN'
            });
        }
        if (process.env.NODE_ENV === 'development' && token === 'mock-token') {
            req.user = {
                id: 'mock-user-id',
                employeeId: null,
                email: '<EMAIL>',
                firstName: 'Admin',
                lastName: 'User',
                role: 'super_admin',
                permissions: ['super_admin', 'hr_admin', 'hr', 'manager', 'employee_read_all', 'employee_update_all', 'department_read', 'department_write'],
                departmentId: null,
                managerId: null,
                sessionId: 'mock-session',
                deviceFingerprint: 'mock-device'
            };
            return next();
        }
        const userResult = await db.query(`
      SELECT
        u.id,
        u.email,
        u.role,
        u.is_active,
        u.last_login,
        e.id as employee_id,
        CASE
          WHEN e.id IS NULL THEN 'Admin'
          ELSE COALESCE(
            (SELECT pgp_sym_decrypt(e.first_name_encrypted, $2) WHERE e.first_name_encrypted IS NOT NULL),
            'Unknown'
          )
        END as first_name,
        CASE
          WHEN e.id IS NULL THEN 'User'
          ELSE COALESCE(
            (SELECT pgp_sym_decrypt(e.last_name_encrypted, $2) WHERE e.last_name_encrypted IS NOT NULL),
            'Unknown'
          )
        END as last_name,
        e.department_id,
        e.manager_id,
        e.status as employee_status
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.id = $1 AND u.is_active = true
    `, [tokenPayload.userId, process.env.ENCRYPTION_KEY || 'default-key']);
        if (userResult.rows.length === 0) {
            logger_1.logger.warn('User not found or inactive during authentication', {
                userId: tokenPayload.userId,
                ip: req.ip
            });
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'User not found or inactive',
                code: 'USER_NOT_FOUND'
            });
        }
        const user = userResult.rows[0];
        if (user.employee_id && user.employee_status !== 'active') {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Employee account is not active',
                code: 'EMPLOYEE_INACTIVE'
            });
        }
        const currentDeviceFingerprint = req.headers['x-device-fingerprint'];
        if (tokenPayload.deviceFingerprint && currentDeviceFingerprint) {
            if (tokenPayload.deviceFingerprint !== currentDeviceFingerprint) {
                logger_1.logger.warn('Device fingerprint mismatch', {
                    userId: user.id,
                    sessionId: tokenPayload.sessionId,
                    ip: req.ip,
                    userAgent: req.get('User-Agent')
                });
            }
        }
        req.user = {
            id: user.id,
            employeeId: user.employee_id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            role: user.role,
            permissions: tokenPayload.permissions,
            departmentId: user.department_id,
            managerId: user.manager_id,
            sessionId: tokenPayload.sessionId,
            deviceFingerprint: tokenPayload.deviceFingerprint
        };
        await db.query('UPDATE users SET last_activity = NOW() WHERE id = $1', [user.id]);
        next();
    }
    catch (error) {
        logger_1.logger.error('Authentication middleware error:', {
            error: error.message,
            stack: error.stack,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path
        });
        return res.status(500).json({
            error: 'Internal Server Error',
            message: 'Authentication error',
            code: 'AUTH_ERROR'
        });
    }
};
exports.authMiddleware = authMiddleware;
const optionalAuthMiddleware = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next();
        }
        await (0, exports.authMiddleware)(req, res, next);
    }
    catch (error) {
        next();
    }
};
exports.optionalAuthMiddleware = optionalAuthMiddleware;
async function getUserPermissions(role) {
    const rolePermissions = {
        'super_admin': [
            'super_admin', 'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
        ],
        'hr_admin': [
            'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
        ],
        'hr': [
            'hr', 'employee', 'recruiter'
        ],
        'manager': [
            'manager', 'employee'
        ],
        'employee': [
            'employee'
        ],
        'recruiter': [
            'recruiter', 'employee'
        ],
        'payroll': [
            'payroll', 'employee'
        ],
        'it': [
            'it', 'employee'
        ]
    };
    return rolePermissions[role] || ['employee'];
}
const requireRole = (...allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required'
            });
        }
        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).json({
                error: 'Forbidden',
                message: 'Insufficient role permissions'
            });
        }
        next();
    };
};
exports.requireRole = requireRole;
const requirePermission = (...requiredPermissions) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required'
            });
        }
        const hasPermission = requiredPermissions.some(permission => req.user.permissions.includes(permission));
        if (!hasPermission) {
            return res.status(403).json({
                error: 'Forbidden',
                message: 'Insufficient permissions'
            });
        }
        next();
    };
};
exports.requirePermission = requirePermission;
const requireDepartmentAccess = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication required'
        });
    }
    if (req.user.permissions.includes('hr_admin') || req.user.permissions.includes('hr')) {
        return next();
    }
    if (req.user.permissions.includes('manager') && req.user.departmentId) {
        return next();
    }
    if (req.user.permissions.includes('employee')) {
        return next();
    }
    return res.status(403).json({
        error: 'Forbidden',
        message: 'Department access denied'
    });
};
exports.requireDepartmentAccess = requireDepartmentAccess;
const requireSelfOrManagerAccess = (employeeIdParam = 'employeeId') => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required'
            });
        }
        const targetEmployeeId = req.params[employeeIdParam] || req.body[employeeIdParam];
        if (req.user.permissions.includes('hr_admin') || req.user.permissions.includes('hr')) {
            return next();
        }
        if (req.user.permissions.includes('manager')) {
            return next();
        }
        if (req.user.employeeId === targetEmployeeId) {
            return next();
        }
        return res.status(403).json({
            error: 'Forbidden',
            message: 'Access denied'
        });
    };
};
exports.requireSelfOrManagerAccess = requireSelfOrManagerAccess;
//# sourceMappingURL=auth.js.map