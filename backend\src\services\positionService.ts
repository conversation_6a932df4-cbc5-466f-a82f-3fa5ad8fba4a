import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export interface Position {
  id: string
  title: string
  description?: string
  departmentId: string
  departmentName?: string
  level: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'manager' | 'director' | 'vp' | 'c_level'
  salaryMin?: number
  salaryMax?: number
  currency?: string
  requiredSkills?: string[]
  isActive: boolean
  createdAt: Date
  employeeCount?: number
}

export interface CreatePositionData {
  title: string
  description?: string
  departmentId: string
  level: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'manager' | 'director' | 'vp' | 'c_level'
  salaryMin?: number
  salaryMax?: number
  currency?: string
  requiredSkills?: string[]
}

export interface UpdatePositionData extends Partial<CreatePositionData> {
  isActive?: boolean
}

export interface PositionFilters {
  departmentId?: string
  level?: string
  type?: string
  includeInactive?: boolean
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export class PositionService {
  private db: DatabaseService

  constructor() {
    // Initialize database connection
    this.db = new DatabaseService()
  }

  async getAllPositions(filters: PositionFilters, user: any): Promise<Position[]> {
    try {
      const params: any[] = []
      let paramCount = 0

      let query = `
        SELECT
          p.*,
          d.name as department_name,
          COUNT(e.id) as employee_count
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN employees e ON e.position_id = p.id AND e.status = 'active'
        WHERE 1=1
      `

      if (!filters.includeInactive) {
        query += ` AND p.is_active = true`
      }

      if (filters.departmentId) {
        paramCount++
        query += ` AND p.department_id = $${paramCount}`
        params.push(filters.departmentId)
      }

      if (filters.level) {
        paramCount++
        query += ` AND p.level = $${paramCount}`
        params.push(filters.level)
      }

      // Removed filters.type since 'type' column doesn't exist in database

      if (filters.search) {
        paramCount++
        query += ` AND (p.title ILIKE $${paramCount} OR p.description ILIKE $${paramCount})`
        params.push(`%${filters.search}%`)
      }

      query += ` GROUP BY p.id, d.name`

      // Add sorting
      const sortBy = filters.sortBy || 'title'
      const sortOrder = filters.sortOrder || 'asc'
      query += ` ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}`

      const result = await this.db.query(query, params)
      
      return result.rows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        departmentId: row.department_id,
        departmentName: row.department_name,
        level: row.level,
        salaryMin: row.min_salary,
        salaryMax: row.max_salary,
        currency: row.currency,
        requiredSkills: row.required_skills || [],
        isActive: row.is_active,
        createdAt: row.created_at,
        employeeCount: parseInt(row.employee_count) || 0
      }))
    } catch (error) {
      logger.error('Error fetching positions:', error)
      throw new Error('Failed to fetch positions')
    }
  }

  async getPositionById(positionId: string, user: any): Promise<Position | null> {
    try {
      const query = `
        SELECT 
          p.*,
          d.name as department_name,
          COUNT(e.id) as employee_count,
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN employees e ON e.position_id = p.id AND e.status = 'active'
        WHERE p.id = $1
        GROUP BY p.id, d.name
      `
      
      const result = await this.db.query(query, [positionId])
      
      if (result.rows.length === 0) {
        return null
      }

      const row = result.rows[0]
      return {
        id: row.id,
        title: row.title,
        description: row.description,
        departmentId: row.department_id,
        departmentName: row.department_name,
        level: row.level,
        salaryMin: row.min_salary,
        salaryMax: row.max_salary,
        currency: row.currency,
        requiredSkills: row.required_skills || [],
        isActive: row.is_active,
        createdAt: row.created_at,
        employeeCount: parseInt(row.employee_count) || 0
      }
    } catch (error) {
      logger.error('Error fetching position by ID:', error)
      throw new Error('Failed to fetch position')
    }
  }

  async createPosition(data: CreatePositionData, userId: string): Promise<Position> {
    try {
      // Check if position with same title in same department already exists
      const existingQuery = `
        SELECT id FROM positions 
        WHERE title = $1 AND department_id = $2 AND is_active = true
      `
      const existing = await this.db.query(existingQuery, [data.title, data.departmentId])
      
      if (existing.rows.length > 0) {
        throw new Error('Position with this title already exists in the department')
      }

      const query = `
        INSERT INTO positions (
          title, description, department_id, level,
          min_salary, max_salary, currency, required_skills
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `
      
      const values = [
        data.title,
        data.description,
        data.departmentId,
        data.level,
        data.salaryMin,
        data.salaryMax,
        data.currency || 'USD',
        data.requiredSkills || []
      ]
      
      const result = await this.db.query(query, values)
      const position = result.rows[0]
      
      return {
        id: position.id,
        title: position.title,
        description: position.description,
        departmentId: position.department_id,
        departmentName: undefined, // Not available in create operation
        level: position.level,
        salaryMin: position.min_salary,
        salaryMax: position.max_salary,
        currency: position.currency,
        requiredSkills: position.required_skills || [],
        isActive: position.is_active,
        createdAt: position.created_at,
        employeeCount: 0 // New position has no employees
      }
    } catch (error) {
      logger.error('Error creating position:', error)
      throw error
    }
  }

  async updatePosition(positionId: string, data: UpdatePositionData, userId: string): Promise<Position | null> {
    try {
      // Check if position exists
      const existingPos = await this.getPositionById(positionId, { id: userId })
      if (!existingPos) {
        return null
      }

      // Check for title conflicts if title is being updated
      if (data.title && data.title !== existingPos.title) {
        const conflictQuery = `
          SELECT id FROM positions 
          WHERE title = $1 AND department_id = $2 AND id != $3 AND is_active = true
        `
        const conflict = await this.db.query(conflictQuery, [
          data.title,
          data.departmentId || existingPos.departmentId,
          positionId
        ])
        
        if (conflict.rows.length > 0) {
          throw new Error('Position with this title already exists in the department')
        }
      }

      const updateFields = []
      const values = []
      let paramCount = 0

      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined) {
          paramCount++
          let dbKey = key.replace(/([A-Z])/g, '_$1').toLowerCase()
          
          // Handle array fields
          if (['requiredSkills', 'preferredSkills', 'responsibilities', 'requirements'].includes(key)) {
            updateFields.push(`${dbKey} = $${paramCount}`)
            values.push(JSON.stringify(value))
          } else {
            updateFields.push(`${dbKey} = $${paramCount}`)
            values.push(value)
          }
        }
      })

      if (updateFields.length === 0) {
        return existingPos
      }

      paramCount++
      updateFields.push(`updated_at = $${paramCount}`)
      values.push(new Date())

      paramCount++
      values.push(positionId)

      const query = `
        UPDATE positions 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `
      
      const result = await this.db.query(query, values)
      const position = result.rows[0]
      
      return {
        id: position.id,
        title: position.title,
        description: position.description,
        departmentId: position.department_id,
        departmentName: undefined, // Not available in update operation
        level: position.level,
        salaryMin: position.min_salary,
        salaryMax: position.max_salary,
        currency: position.currency,
        requiredSkills: position.required_skills || [],
        isActive: position.is_active,
        createdAt: position.created_at,
        employeeCount: undefined // Not calculated in update operation
      }
    } catch (error) {
      logger.error('Error updating position:', error)
      throw error
    }
  }

  async deactivatePosition(positionId: string, userId: string): Promise<boolean> {
    try {
      // Check if position has active employees
      const employeeCheck = await this.db.query(
        'SELECT COUNT(*) as count FROM employees WHERE position_id = $1 AND status = $2',
        [positionId, 'active']
      )
      
      if (parseInt(employeeCheck.rows[0].count) > 0) {
        throw new Error('Cannot deactivate position with active employees')
      }

      const query = `
        UPDATE positions
        SET is_active = false
        WHERE id = $1 AND is_active = true
        RETURNING id
      `
      
      const result = await this.db.query(query, [positionId])
      return result.rows.length > 0
    } catch (error) {
      logger.error('Error deactivating position:', error)
      throw error
    }
  }

  async getPositionEmployees(positionId: string, filters: any, user: any): Promise<any[]> {
    try {
      let query = `
        SELECT e.*, d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE e.position_id = $1
      `
      
      const params = [positionId]
      let paramCount = 1

      if (filters.status) {
        paramCount++
        query += ` AND e.status = $${paramCount}`
        params.push(filters.status)
      } else {
        query += ` AND e.status = 'active'`
      }

      // Add encryption key for ordering
      const encryptionKey = process.env.ENCRYPTION_KEY || 'default-key'
      params.push(encryptionKey)
      paramCount++

      query += ` ORDER BY pgp_sym_decrypt(e.first_name_encrypted, $${paramCount}), pgp_sym_decrypt(e.last_name_encrypted, $${paramCount})`

      const result = await this.db.query(query, params)
      return result.rows
    } catch (error) {
      logger.error('Error fetching position employees:', error)
      throw new Error('Failed to fetch position employees')
    }
  }

  async getPositionsByDepartment(departmentId: string, filters: any, user: any): Promise<Position[]> {
    try {
      let query = `
        SELECT 
          p.*,
          d.name as department_name,
          COUNT(e.id) as employee_count
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN employees e ON e.position_id = p.id AND e.status = 'active'
        WHERE p.department_id = $1
      `
      
      const params = [departmentId]

      if (!filters.includeInactive) {
        query += ` AND p.is_active = true`
      }

      query += ` GROUP BY p.id, d.name ORDER BY p.title`

      const result = await this.db.query(query, params)
      
      return result.rows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        departmentId: row.department_id,
        departmentName: row.department_name,
        level: row.level,
        salaryMin: row.min_salary,
        salaryMax: row.max_salary,
        currency: row.currency,
        requiredSkills: row.required_skills || [],
        isActive: row.is_active,
        createdAt: row.created_at,
        employeeCount: parseInt(row.employee_count) || 0
      }))
    } catch (error) {
      logger.error('Error fetching positions by department:', error)
      throw new Error('Failed to fetch positions by department')
    }
  }

  async getPositionAnalytics(filters: any, user: any): Promise<any> {
    try {
      let query = `
        SELECT
          COUNT(*) as total_positions,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_positions,
          AVG(min_salary) as avg_salary_min,
          AVG(max_salary) as avg_salary_max,
          (SELECT COUNT(*) FROM employees WHERE status = 'active') as total_employees
        FROM positions
      `
      
      const params: any[] = []
      let paramCount = 0

      if (filters.departmentId) {
        paramCount++
        query += ` WHERE department_id = $${paramCount}`
        params.push(filters.departmentId)
      }
      
      const result = await this.db.query(query, params)
      return result.rows[0]
    } catch (error) {
      logger.error('Error fetching position analytics:', error)
      throw new Error('Failed to fetch position analytics')
    }
  }
}
