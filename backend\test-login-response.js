const axios = require('axios');

async function testLoginResponse() {
  console.log('🔍 Testing awadhesh Login Response');
  console.log('=' .repeat(50));
  
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'awadhesh123'
    };
    
    console.log('📤 Sending login request...');
    const response = await axios.post('http://localhost:3002/api/auth/login', loginData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Login successful!');
    console.log('\n📋 COMPLETE RESPONSE DATA:');
    console.log(JSON.stringify(response.data, null, 2));
    
    console.log('\n🎯 KEY INFORMATION:');
    console.log('   User ID:', response.data.user.id);
    console.log('   Email:', response.data.user.email);
    console.log('   First Name:', response.data.user.firstName);
    console.log('   Last Name:', response.data.user.lastName);
    console.log('   Role:', response.data.user.role);
    console.log('   Employee ID:', response.data.user.employeeId);
    console.log('   Permissions Count:', response.data.user.permissions.length);
    console.log('   Permissions:', response.data.user.permissions);
    
    console.log('\n🔍 ROLE ANALYSIS:');
    if (response.data.user.role === 'super_admin') {
      console.log('✅ Backend is returning correct role: super_admin');
      console.log('🔧 Issue is likely in frontend role display logic');
    } else {
      console.log('❌ Backend is returning incorrect role:', response.data.user.role);
      console.log('🔧 Need to fix backend role assignment');
    }
    
    console.log('\n🛡️  PERMISSIONS ANALYSIS:');
    const expectedSuperAdminPermissions = [
      'super_admin', 'system_admin', 'hr_admin', 'hr', 'manager', 
      'employee', 'recruiter', 'payroll', 'it'
    ];
    
    const hasAllPermissions = expectedSuperAdminPermissions.every(perm => 
      response.data.user.permissions.includes(perm)
    );
    
    if (hasAllPermissions) {
      console.log('✅ All expected super admin permissions present');
    } else {
      console.log('⚠️  Some super admin permissions missing');
      const missing = expectedSuperAdminPermissions.filter(perm => 
        !response.data.user.permissions.includes(perm)
      );
      console.log('   Missing:', missing);
    }
    
  } catch (error) {
    console.error('❌ Login failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    } else {
      console.error('   Error:', error.message);
    }
  }
}

testLoginResponse();
