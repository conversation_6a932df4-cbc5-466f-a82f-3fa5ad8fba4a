export interface Position {
    id: string;
    title: string;
    description?: string;
    departmentId: string;
    departmentName?: string;
    level: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'manager' | 'director' | 'vp' | 'c_level';
    salaryMin?: number;
    salaryMax?: number;
    currency?: string;
    requiredSkills?: string[];
    isActive: boolean;
    createdAt: Date;
    employeeCount?: number;
}
export interface CreatePositionData {
    title: string;
    description?: string;
    departmentId: string;
    level: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'manager' | 'director' | 'vp' | 'c_level';
    salaryMin?: number;
    salaryMax?: number;
    currency?: string;
    requiredSkills?: string[];
}
export interface UpdatePositionData extends Partial<CreatePositionData> {
    isActive?: boolean;
}
export interface PositionFilters {
    departmentId?: string;
    level?: string;
    type?: string;
    includeInactive?: boolean;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class PositionService {
    private db;
    constructor();
    getAllPositions(filters: PositionFilters, user: any): Promise<Position[]>;
    getPositionById(positionId: string, user: any): Promise<Position | null>;
    createPosition(data: CreatePositionData, userId: string): Promise<Position>;
    updatePosition(positionId: string, data: UpdatePositionData, userId: string): Promise<Position | null>;
    deactivatePosition(positionId: string, userId: string): Promise<boolean>;
    getPositionEmployees(positionId: string, filters: any, user: any): Promise<any[]>;
    getPositionsByDepartment(departmentId: string, filters: any, user: any): Promise<Position[]>;
    getPositionAnalytics(filters: any, user: any): Promise<any>;
}
//# sourceMappingURL=positionService.d.ts.map