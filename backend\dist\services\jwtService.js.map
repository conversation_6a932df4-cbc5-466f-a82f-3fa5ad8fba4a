{"version": 3, "file": "jwtService.js", "sourceRoot": "", "sources": ["../../src/services/jwtService.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAA8B;AAC9B,oDAA2B;AAC3B,4CAAwC;AACxC,uDAAmD;AAEnD,MAAM,EAAE,GAAG,IAAI,iCAAe,EAAE,CAAA;AAmChC,MAAM,UAAU;IAYd;QACE,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,wBAAwB,CAAA;QAClF,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,yBAAyB,CAAA;QACrF,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK,CAAA;QAC/D,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,CAAA;QAChE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,gBAAgB,CAAA;QACxD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,mBAAmB,CAAA;QAC/D,IAAI,CAAC,SAAS,GAAG,OAAO,CAAA;QAExB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC,wBAAwB,EAAE,CAAA;QACjC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,MAAc,EACd,KAAa,EACb,IAAY,EACZ,WAAqB,EACrB,SAAiB,EACjB,iBAAyB,EACzB,SAAiB,EACjB,SAAiB;QAEjB,IAAI,CAAC;YAEH,MAAM,GAAG,GAAG,gBAAM,CAAC,UAAU,EAAE,CAAA;YAG/B,MAAM,aAAa,GAAiB;gBAClC,MAAM;gBACN,KAAK;gBACL,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,iBAAiB;gBACjB,GAAG;gBACH,GAAG,EAAE,IAAI,CAAC,MAAM;gBAChB,GAAG,EAAE,IAAI,CAAC,QAAQ;aACnB,CAAA;YAGD,MAAM,WAAW,GAAG,sBAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAClE,SAAS,EAAE,IAAI,CAAC,iBAA2B;gBAC3C,SAAS,EAAE,IAAI,CAAC,SAA0B;aACxB,CAAC,CAAA;YAGrB,MAAM,cAAc,GAAG,gBAAM,CAAC,UAAU,EAAE,CAAA;YAC1C,MAAM,YAAY,GAAG,sBAAG,CAAC,IAAI,CAC3B;gBACE,MAAM;gBACN,SAAS;gBACT,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE,SAAS;gBACf,GAAG,EAAE,IAAI,CAAC,MAAM;gBAChB,GAAG,EAAE,IAAI,CAAC,QAAQ;aACnB,EACD,IAAI,CAAC,kBAAkB,EACvB;gBACE,SAAS,EAAE,IAAI,CAAC,kBAA4B;gBAC5C,SAAS,EAAE,IAAI,CAAC,SAA0B;aACxB,CACrB,CAAA;YAGD,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC3B,EAAE,EAAE,cAAc;gBAClB,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC3E,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAA;YAGF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAA;YAEjE,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,MAAM;gBACN,SAAS;gBACT,iBAAiB,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;gBAC5D,SAAS;aACV,CAAC,CAAA;YAEF,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,SAAS;gBACT,SAAS,EAAE,QAAQ;aACpB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACrD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC;YAEH,IAAI,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC3C,CAAC;YAGD,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxD,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAiB,CAAA;YAGlB,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;YAC1C,CAAC;YAGD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;YAC5D,CAAC;YAED,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;aACtC,CAAC,CAAA;YACF,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,iBAAyB;QACtE,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAChE,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAQ,CAAA;YAET,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;YACvC,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC/D,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;YACnD,CAAC;YAGD,IAAI,WAAW,CAAC,iBAAiB,KAAK,iBAAiB,EAAE,CAAC;gBACxD,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;oBAC9D,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;oBAChC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;oBACxE,mBAAmB,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;iBAC/D,CAAC,CAAA;gBACF,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;YAChD,CAAC;YAGD,IAAI,WAAW,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBACvC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC9C,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YAGD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;YACvD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACnC,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAC/C,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,WAAW,CAAC,SAAS,EACrB,iBAAiB,EACjB,WAAW,CAAC,SAAS,EACrB,WAAW,CAAC,SAAS,CACtB,CAAA;YAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAE9C,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC,CAAA;YAEF,OAAO,YAAY,CAAA;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YACnD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC;YAEH,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAGpC,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,OAAe;QACtC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,CACZ,mFAAmF,EACnF,CAAC,OAAO,CAAC,CACV,CAAA;YAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,CACZ,6EAA6E,EAC7E,CAAC,SAAS,CAAC,CACZ,CAAA;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,SAA2B;QACzD,MAAM,KAAK,GAAG;;;;;;;;;;;;KAYb,CAAA;QAED,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE;YACpB,SAAS,CAAC,MAAM;YAChB,SAAS,CAAC,SAAS;YACnB,SAAS,CAAC,EAAE;YACZ,SAAS,CAAC,SAAS;YACnB,SAAS,CAAC,SAAS;YACnB,SAAS,CAAC,SAAS;YACnB,SAAS,CAAC,SAAS;SACpB,CAAC,CAAA;IACJ,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,OAAe;QAC3C,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,KAAK,CAC3B,yDAAyD,EACzD,CAAC,OAAO,CAAC,CACV,CAAA;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC1B,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,gBAAgB;YACxB,MAAM,EAAE,GAAG,CAAC,OAAO;YACnB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,iBAAiB,EAAE,GAAG,CAAC,kBAAkB;YACzC,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,qBAAqB;YACpC,SAAS,EAAE,GAAG,CAAC,UAAU;SAC1B,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC7C,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,KAAK,CAC3B,qFAAqF,EACrF,CAAC,SAAS,CAAC,CACZ,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;IAC/B,CAAC;IAKO,KAAK,CAAC,WAAW,CAAC,MAAc;QACtC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,KAAK,CAC3B,sEAAsE,EACtE,CAAC,MAAM,CAAC,CACT,CAAA;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAG3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAE5D,OAAO;YACL,GAAG,IAAI;YACP,WAAW;SACZ,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAE3C,MAAM,eAAe,GAA6B;YAChD,aAAa,EAAE;gBACb,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI;aACrG;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS;aAChE;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,UAAU,EAAE,WAAW;aAC9B;YACD,SAAS,EAAE;gBACT,SAAS,EAAE,UAAU;aACtB;YACD,UAAU,EAAE;gBACV,UAAU;aACX;SACF,CAAA;QAED,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC9C,CAAC;IAKO,WAAW,CAAC,MAAc;QAChC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7B,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;QAE3C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,IAAI,CAAA;YAC7B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,GAAG,IAAI,CAAA;YAClC,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YACvC,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YAC5C,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAChC,CAAC;IACH,CAAC;IAKO,wBAAwB;QAC9B,MAAM,eAAe,GAAG;YACtB,mBAAmB;YACnB,oBAAoB;YACpB,YAAY;YACZ,cAAc;SACf,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,MAAM,EAAE,CAAC,CAAA;YACrE,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAA;QAC1E,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;QAC3E,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,yBAAyB,CAAC,SAAiB,EAAE,iBAAyB,EAAE,EAAE,iBAAyB,EAAE;QAC1G,MAAM,IAAI,GAAG,GAAG,SAAS,IAAI,cAAc,IAAI,cAAc,EAAE,CAAA;QAC/D,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAC/D,CAAC;IAKD,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,KAAK,CAC3B,oFAAoF,CACrF,CAAA;YAED,eAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,QAAQ,yBAAyB,CAAC,CAAA;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;QAC1D,CAAC;IACH,CAAC;;AAIM,gCAAU;AAhcF,yBAAc,GAAG,IAAI,GAAG,EAAU,CAAA;AA+btC,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAA;AAE1C,kBAAe,kBAAU,CAAA"}