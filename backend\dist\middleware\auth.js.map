{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AAEA,iEAA6D;AAC7D,4CAAwC;AACxC,uDAAiE;AACjE,sDAAkD;AAyBlD,MAAM,EAAE,GAAG,IAAI,iCAAe,EAAE,CAAA;AAMzB,MAAM,cAAc,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACtF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;QAE5C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,uCAAuC;gBAChD,IAAI,EAAE,eAAe;aACtB,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAGrC,IAAI,YAA0B,CAAA;QAC9B,IAAI,CAAC;YACH,YAAY,GAAG,MAAM,uBAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,IAAI,EAAE,GAAG,CAAC,IAAI;aACf,CAAC,CAAA;YAGF,MAAM,yBAAW,CAAC,YAAY,CAC5B,cAAc,EACd,SAAS,EACT,SAAS,EACT,GAAG,EACH,EAAE,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAClD,CAAA;YAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAErE,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,cAAc;gBAClB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,sBAAsB;gBAC7B,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;gBAC5I,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,cAAc;gBACzB,iBAAiB,EAAE,aAAa;aACjC,CAAA;YACD,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BjC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,aAAa,CAAC,CAAC,CAAA;QAEtE,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;gBAC9D,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,EAAE,EAAE,GAAG,CAAC,EAAE;aACX,CAAC,CAAA;YAEF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE,gBAAgB;aACvB,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAG/B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YAC1D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,gCAAgC;gBACzC,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAA;QACJ,CAAC;QAGD,MAAM,wBAAwB,GAAG,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAW,CAAA;QAC9E,IAAI,YAAY,CAAC,iBAAiB,IAAI,wBAAwB,EAAE,CAAC;YAC/D,IAAI,YAAY,CAAC,iBAAiB,KAAK,wBAAwB,EAAE,CAAC;gBAChE,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACzC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,EAAE,EAAE,GAAG,CAAC,EAAE;oBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;iBACjC,CAAC,CAAA;YAIJ,CAAC;QACH,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;SAClD,CAAA;QAGD,MAAM,EAAE,CAAC,KAAK,CACZ,sDAAsD,EACtD,CAAC,IAAI,CAAC,EAAE,CAAC,CACV,CAAA;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;YAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC,CAAA;QAEF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,sBAAsB;YAC/B,IAAI,EAAE,YAAY;SACnB,CAAC,CAAA;IACJ,CAAC;AACH,CAAC,CAAA;AAzKY,QAAA,cAAc,kBAyK1B;AAMM,MAAM,sBAAsB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC9F,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;QAE5C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAGD,MAAM,IAAA,sBAAc,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAA;IACR,CAAC;AACH,CAAC,CAAA;AAdY,QAAA,sBAAsB,0BAclC;AAKD,KAAK,UAAU,kBAAkB,CAAC,IAAY;IAC5C,MAAM,eAAe,GAA6B;QAChD,aAAa,EAAE;YACb,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI;SACrG;QACD,UAAU,EAAE;YACV,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS;SAChE;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU,EAAE,WAAW;SAC9B;QACD,SAAS,EAAE;YACT,SAAS,EAAE,UAAU;SACtB;QACD,UAAU,EAAE;YACV,UAAU;SACX;QACD,WAAW,EAAE;YACX,WAAW,EAAE,UAAU;SACxB;QACD,SAAS,EAAE;YACT,SAAS,EAAE,UAAU;SACtB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;SACjB;KACF,CAAA;IAED,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;AAC9C,CAAC;AAKM,MAAM,WAAW,GAAG,CAAC,GAAG,YAAsB,EAAE,EAAE;IACvD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,+BAA+B;aACzC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAA;AAlBY,QAAA,WAAW,eAkBvB;AAKM,MAAM,iBAAiB,GAAG,CAAC,GAAG,mBAA6B,EAAE,EAAE;IACpE,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAC1D,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC1C,CAAA;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAA;AAtBY,QAAA,iBAAiB,qBAsB7B;AAKM,MAAM,uBAAuB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACzF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,yBAAyB;SACnC,CAAC,CAAA;IACJ,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACrF,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtE,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9C,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QAC1B,KAAK,EAAE,WAAW;QAClB,OAAO,EAAE,0BAA0B;KACpC,CAAC,CAAA;AACJ,CAAC,CAAA;AA3BY,QAAA,uBAAuB,2BA2BnC;AAMM,MAAM,0BAA0B,GAAG,CAAC,kBAA0B,YAAY,EAAE,EAAE;IACnF,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAGjF,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACrF,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,KAAK,gBAAgB,EAAE,CAAC;YAC7C,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE,eAAe;SACzB,CAAC,CAAA;IACJ,CAAC,CAAA;AACH,CAAC,CAAA;AA/BY,QAAA,0BAA0B,8BA+BtC"}