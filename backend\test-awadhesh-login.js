const axios = require('axios');

async function testAwadheshLogin() {
  try {
    console.log('🧪 Testing awadhesh user login...');
    console.log('🌐 URL: http://localhost:3002/api/auth/login');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'awadhesh123'
    };
    
    console.log('📤 Sending login request...');
    console.log('   Email:', loginData.email);
    console.log('   Password:', loginData.password);
    
    const response = await axios.post('http://localhost:3002/api/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n✅ Login successful!');
    console.log('📊 Response status:', response.status);
    console.log('📊 User data:', JSON.stringify(response.data.user, null, 2));
    console.log('📊 Permissions:', response.data.user.permissions);
    
    // Test accessing a protected endpoint
    if (response.data.tokens.accessToken) {
      console.log('\n🔐 Testing protected endpoint access...');
      
      const protectedResponse = await axios.get('http://localhost:3002/api/employees', {
        headers: {
          'Authorization': `Bearer ${response.data.tokens.accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Protected endpoint access successful!');
      console.log('📊 Employees endpoint status:', protectedResponse.status);
      console.log('📊 Number of employees returned:', protectedResponse.data.data?.length || 0);
    }
    
    console.log('\n🎉 awadhesh user authentication is working perfectly!');
    
  } catch (error) {
    console.error('❌ Login failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    } else {
      console.error('   Error:', error.message);
    }
  }
}

testAwadheshLogin();
