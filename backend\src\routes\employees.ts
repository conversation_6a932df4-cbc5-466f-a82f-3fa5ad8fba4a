
import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { EmployeeService } from '../services/employeeService'
import { authMiddleware, requirePermission, requireSelfOrManagerAccess } from '../middleware/auth'
import {
  requireResourcePermission,
  requireMinimumRole,
  requirePermissionBasedRateLimit,
  attachUserPermissions
} from '../middleware/enhancedRBAC'
import { auditMiddleware } from '../middleware/audit'
import { logger } from '../utils/logger'

const router = express.Router()
const employeeService = new EmployeeService()

// Apply authentication to all routes
router.use(authMiddleware)
// Temporarily disable enhanced RBAC for development
// router.use(attachUserPermissions)

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route GET /api/employees
 * @desc Get all employees with filtering and pagination
 * @access HR, Manager
 */
router.get('/',
  // Temporarily use simple permission system for development
  requirePermission('super_admin', 'hr_admin', 'hr', 'manager'),
  // requireResourcePermission('employees', 'read', { scope: 'all' }),
  // requirePermissionBasedRateLimit({
  //   'hr_admin': { requests: 1000, windowMs: 60000 },
  //   'hr_manager': { requests: 500, windowMs: 60000 },
  //   'manager': { requests: 100, windowMs: 60000 },
  //   'employee': { requests: 50, windowMs: 60000 }
  // }),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isString(),
    query('department').optional().isUUID(),
    query('status').optional().isIn(['active', 'inactive', 'terminated']),
    query('role').optional().isString(),
    query('manager').optional().isUUID(),
    query('sortBy').optional().isIn(['firstName', 'lastName', 'email', 'hireDate', 'department']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  validateRequest,
  auditMiddleware('employee_list', 'data_access'),
  async (req, res) => {
    try {
      const employees = await employeeService.getEmployees(req.query, req.user)
      res.json(employees)
    } catch (error) {
      logger.error('Error fetching employees:', error)
      res.status(500).json({ error: 'Failed to fetch employees' })
    }
  }
)

/**
 * @route GET /api/employees/managers
 * @desc Get all managers for dropdown selection
 * @access HR, Manager
 */
router.get('/managers',
  requirePermission('super_admin', 'hr_admin', 'hr', 'manager'),
  async (req, res) => {
    try {
      const managers = await employeeService.getManagers(req.user)
      res.json(managers)
    } catch (error) {
      logger.error('Error fetching managers:', error)
      res.status(500).json({ error: 'Failed to fetch managers' })
    }
  }
)

/**
 * @route GET /api/employees/:employeeId
 * @desc Get employee by ID
 * @access HR, Manager, Self
 */
router.get('/:employeeId',
  requireSelfOrManagerAccess('employeeId'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const employee = await employeeService.getEmployeeById(employeeId, req.user)

      if (!employee) {
        return res.status(404).json({ error: 'Employee not found' })
      }

      res.json(employee)
    } catch (error) {
      logger.error('Error fetching employee:', error)
      res.status(500).json({ error: 'Failed to fetch employee' })
    }
  }
)

/**
 * @route POST /api/employees
 * @desc Create new employee
 * @access HR Admin
 */
router.post('/',
  requirePermission('hr_admin'),
  [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('personalEmail').optional().isEmail(),
    body('phone').optional().isMobilePhone('any'),
    body('dateOfBirth').optional().isISO8601(),
    body('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']),
    body('address').optional().isObject(),
    body('emergencyContact').optional().isObject(),
    body('departmentId').isUUID().withMessage('Department ID is required'),
    body('positionId').isUUID().withMessage('Position ID is required'),
    body('managerId').optional().isUUID(),
    body('employeeType').isIn(['full_time', 'part_time', 'contract', 'intern']),
    body('hireDate').isISO8601().withMessage('Valid hire date is required'),
    body('salary').optional().isNumeric(),
    body('currency').optional().isString(),
    body('workLocation').optional().isIn(['office', 'remote', 'hybrid']),
    body('skills').optional().isArray(),
    body('certifications').optional().isArray()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const employee = await employeeService.createEmployee(req.body, req.user.id)
      
      logger.info(`Employee created: ${employee.id}`, { 
        userId: req.user.id,
        employeeEmail: req.body.email 
      })
      
      res.status(201).json(employee)
    } catch (error) {
      logger.error('Error creating employee:', error)
      res.status(500).json({ error: 'Failed to create employee' })
    }
  }
)

/**
 * @route PUT /api/employees/:employeeId
 * @desc Update employee
 * @access HR, Manager, Self (limited fields)
 */
router.put('/:employeeId',
  requireSelfOrManagerAccess('employeeId'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('firstName').optional().notEmpty(),
    body('lastName').optional().notEmpty(),
    body('email').optional().isEmail(),
    body('personalEmail').optional().isEmail(),
    body('phone').optional().isMobilePhone('any'),
    body('address').optional().isObject(),
    body('emergencyContact').optional().isObject(),
    body('departmentId').optional().isUUID(),
    body('positionId').optional().isUUID(),
    body('managerId').optional().isUUID(),
    body('salary').optional().isNumeric(),
    body('workLocation').optional().isIn(['office', 'remote', 'hybrid']),
    body('skills').optional().isArray(),
    body('certifications').optional().isArray()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const employee = await employeeService.updateEmployee(employeeId, req.body, req.user)
      
      if (!employee) {
        return res.status(404).json({ error: 'Employee not found or access denied' })
      }

      logger.info(`Employee updated: ${employeeId}`, { userId: req.user.id })
      res.json(employee)
    } catch (error) {
      logger.error('Error updating employee:', error)
      res.status(500).json({ error: 'Failed to update employee' })
    }
  }
)

/**
 * @route DELETE /api/employees/:employeeId
 * @desc Deactivate employee (soft delete)
 * @access HR Admin
 */
router.delete('/:employeeId',
  requirePermission('hr_admin'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const success = await employeeService.deactivateEmployee(employeeId, req.user.id)
      
      if (!success) {
        return res.status(404).json({ error: 'Employee not found' })
      }

      logger.info(`Employee deactivated: ${employeeId}`, { userId: req.user.id })
      res.json({ message: 'Employee deactivated successfully' })
    } catch (error) {
      logger.error('Error deactivating employee:', error)
      res.status(500).json({ error: 'Failed to deactivate employee' })
    }
  }
)

/**
 * @route GET /api/employees/:employeeId/profile
 * @desc Get employee profile with additional details
 * @access HR, Manager, Self
 */
router.get('/:employeeId/profile',
  requireSelfOrManagerAccess('employeeId'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const profile = await employeeService.getEmployeeProfile(employeeId, req.user)
      
      if (!profile) {
        return res.status(404).json({ error: 'Employee profile not found' })
      }

      res.json(profile)
    } catch (error) {
      logger.error('Error fetching employee profile:', error)
      res.status(500).json({ error: 'Failed to fetch employee profile' })
    }
  }
)

/**
 * @route GET /api/employees/:employeeId/team
 * @desc Get employee's team members
 * @access HR, Manager, Self
 */
router.get('/:employeeId/team',
  requireSelfOrManagerAccess('employeeId'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const team = await employeeService.getEmployeeTeam(employeeId, req.user)
      
      res.json(team)
    } catch (error) {
      logger.error('Error fetching employee team:', error)
      res.status(500).json({ error: 'Failed to fetch employee team' })
    }
  }
)

/**
 * @route GET /api/employees/:employeeId/reports
 * @desc Get employees reporting to this employee
 * @access HR, Manager, Self
 */
router.get('/:employeeId/reports',
  requireSelfOrManagerAccess('employeeId'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const reports = await employeeService.getDirectReports(employeeId, req.user)
      
      res.json(reports)
    } catch (error) {
      logger.error('Error fetching direct reports:', error)
      res.status(500).json({ error: 'Failed to fetch direct reports' })
    }
  }
)

/**
 * @route POST /api/employees/:employeeId/skills
 * @desc Add skills to employee
 * @access HR, Manager, Self
 */
router.post('/:employeeId/skills',
  requireSelfOrManagerAccess('employeeId'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('skills').isArray().withMessage('Skills must be an array'),
    body('skills.*.name').notEmpty().withMessage('Skill name is required'),
    body('skills.*.level').isIn(['beginner', 'intermediate', 'advanced', 'expert']),
    body('skills.*.verified').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)
      
      logger.info(`Skills added to employee: ${employeeId}`, { 
        userId: req.user.id,
        skillsCount: req.body.skills.length 
      })
      
      res.json(skills)
    } catch (error) {
      logger.error('Error adding employee skills:', error)
      res.status(500).json({ error: 'Failed to add employee skills' })
    }
  }
)

/**
 * @route GET /api/employees/analytics/overview
 * @desc Get employee analytics overview
 * @access HR Admin, Manager
 */
router.get('/analytics/overview',
  requirePermission('hr_admin', 'manager'),
  [
    query('department').optional().isUUID(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)
      res.json(analytics)
    } catch (error) {
      logger.error('Error fetching employee analytics:', error)
      res.status(500).json({ error: 'Failed to fetch employee analytics' })
    }
  }
)

/**
 * @route GET /api/employees/search
 * @desc Search employees
 * @access HR, Manager
 */
router.get('/search',
  requirePermission('hr', 'manager'),
  [
    query('q').notEmpty().withMessage('Search query is required'),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('includeInactive').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const results = await employeeService.searchEmployees(req.query, req.user)
      res.json(results)
    } catch (error) {
      logger.error('Error searching employees:', error)
      res.status(500).json({ error: 'Failed to search employees' })
    }
  }
)

export default router


