"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const positionService_1 = require("../services/positionService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
const positionService = new positionService_1.PositionService();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation Error',
            details: errors.array()
        });
    }
    next();
};
router.get('/', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr', 'manager'), [
    (0, express_validator_1.query)('departmentId').optional().isUUID(),
    (0, express_validator_1.query)('level').optional().isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    (0, express_validator_1.query)('type').optional().isIn(['full_time', 'part_time', 'contract', 'intern']),
    (0, express_validator_1.query)('includeInactive').optional().isBoolean(),
    (0, express_validator_1.query)('search').optional().isString(),
    (0, express_validator_1.query)('sortBy').optional().isIn(['title', 'level', 'department', 'createdAt']),
    (0, express_validator_1.query)('sortOrder').optional().isIn(['asc', 'desc'])
], validateRequest, async (req, res) => {
    try {
        const positions = await positionService.getAllPositions(req.query, req.user);
        res.json(positions);
    }
    catch (error) {
        logger_1.logger.error('Error fetching positions:', error);
        res.status(500).json({ error: 'Failed to fetch positions' });
    }
});
router.get('/:positionId', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr', 'manager'), [(0, express_validator_1.param)('positionId').isUUID().withMessage('Invalid position ID')], validateRequest, async (req, res) => {
    try {
        const { positionId } = req.params;
        const position = await positionService.getPositionById(positionId, req.user);
        if (!position) {
            return res.status(404).json({ error: 'Position not found' });
        }
        res.json(position);
    }
    catch (error) {
        logger_1.logger.error('Error fetching position:', error);
        res.status(500).json({ error: 'Failed to fetch position' });
    }
});
router.post('/', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr_admin'), [
    (0, express_validator_1.body)('title').notEmpty().withMessage('Position title is required'),
    (0, express_validator_1.body)('description').optional().isString(),
    (0, express_validator_1.body)('departmentId').isUUID().withMessage('Department ID is required'),
    (0, express_validator_1.body)('level').isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    (0, express_validator_1.body)('type').isIn(['full_time', 'part_time', 'contract', 'intern']),
    (0, express_validator_1.body)('salaryMin').optional().isNumeric(),
    (0, express_validator_1.body)('salaryMax').optional().isNumeric(),
    (0, express_validator_1.body)('currency').optional().isString(),
    (0, express_validator_1.body)('requiredSkills').optional().isArray(),
    (0, express_validator_1.body)('preferredSkills').optional().isArray(),
    (0, express_validator_1.body)('responsibilities').optional().isArray(),
    (0, express_validator_1.body)('requirements').optional().isArray(),
    (0, express_validator_1.body)('reportsTo').optional().isUUID(),
    (0, express_validator_1.body)('isRemoteEligible').optional().isBoolean(),
    (0, express_validator_1.body)('travelRequired').optional().isBoolean()
], validateRequest, async (req, res) => {
    try {
        const position = await positionService.createPosition(req.body, req.user.id);
        logger_1.logger.info(`Position created: ${position.id}`, {
            userId: req.user.id,
            positionTitle: req.body.title
        });
        res.status(201).json(position);
    }
    catch (error) {
        logger_1.logger.error('Error creating position:', error);
        if (error.message.includes('already exists')) {
            res.status(409).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'Failed to create position' });
        }
    }
});
router.put('/:positionId', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr_admin'), [
    (0, express_validator_1.param)('positionId').isUUID().withMessage('Invalid position ID'),
    (0, express_validator_1.body)('title').optional().notEmpty(),
    (0, express_validator_1.body)('description').optional().isString(),
    (0, express_validator_1.body)('departmentId').optional().isUUID(),
    (0, express_validator_1.body)('level').optional().isIn(['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'vp', 'c_level']),
    (0, express_validator_1.body)('type').optional().isIn(['full_time', 'part_time', 'contract', 'intern']),
    (0, express_validator_1.body)('salaryMin').optional().isNumeric(),
    (0, express_validator_1.body)('salaryMax').optional().isNumeric(),
    (0, express_validator_1.body)('currency').optional().isString(),
    (0, express_validator_1.body)('requiredSkills').optional().isArray(),
    (0, express_validator_1.body)('preferredSkills').optional().isArray(),
    (0, express_validator_1.body)('responsibilities').optional().isArray(),
    (0, express_validator_1.body)('requirements').optional().isArray(),
    (0, express_validator_1.body)('reportsTo').optional().isUUID(),
    (0, express_validator_1.body)('isRemoteEligible').optional().isBoolean(),
    (0, express_validator_1.body)('travelRequired').optional().isBoolean(),
    (0, express_validator_1.body)('isActive').optional().isBoolean()
], validateRequest, async (req, res) => {
    try {
        const { positionId } = req.params;
        const position = await positionService.updatePosition(positionId, req.body, req.user.id);
        if (!position) {
            return res.status(404).json({ error: 'Position not found' });
        }
        logger_1.logger.info(`Position updated: ${positionId}`, { userId: req.user.id });
        res.json(position);
    }
    catch (error) {
        logger_1.logger.error('Error updating position:', error);
        if (error.message.includes('already exists')) {
            res.status(409).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'Failed to update position' });
        }
    }
});
router.delete('/:positionId', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr_admin'), [(0, express_validator_1.param)('positionId').isUUID().withMessage('Invalid position ID')], validateRequest, async (req, res) => {
    try {
        const { positionId } = req.params;
        const success = await positionService.deactivatePosition(positionId, req.user.id);
        if (!success) {
            return res.status(404).json({ error: 'Position not found or has active employees' });
        }
        logger_1.logger.info(`Position deactivated: ${positionId}`, { userId: req.user.id });
        res.json({ message: 'Position deactivated successfully' });
    }
    catch (error) {
        logger_1.logger.error('Error deactivating position:', error);
        res.status(500).json({ error: 'Failed to deactivate position' });
    }
});
router.get('/:positionId/employees', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr', 'manager'), [
    (0, express_validator_1.param)('positionId').isUUID().withMessage('Invalid position ID'),
    (0, express_validator_1.query)('status').optional().isIn(['active', 'inactive', 'terminated'])
], validateRequest, async (req, res) => {
    try {
        const { positionId } = req.params;
        const employees = await positionService.getPositionEmployees(positionId, req.query, req.user);
        res.json(employees);
    }
    catch (error) {
        logger_1.logger.error('Error fetching position employees:', error);
        res.status(500).json({ error: 'Failed to fetch position employees' });
    }
});
router.get('/department/:departmentId', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr', 'manager'), [
    (0, express_validator_1.param)('departmentId').isUUID().withMessage('Invalid department ID'),
    (0, express_validator_1.query)('includeInactive').optional().isBoolean()
], validateRequest, async (req, res) => {
    try {
        const { departmentId } = req.params;
        const positions = await positionService.getPositionsByDepartment(departmentId, req.query, req.user);
        res.json(positions);
    }
    catch (error) {
        logger_1.logger.error('Error fetching department positions:', error);
        res.status(500).json({ error: 'Failed to fetch department positions' });
    }
});
router.get('/analytics/overview', auth_1.authMiddleware, (0, auth_1.requirePermission)('hr_admin'), [
    (0, express_validator_1.query)('departmentId').optional().isUUID(),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], validateRequest, async (req, res) => {
    try {
        const analytics = await positionService.getPositionAnalytics(req.query, req.user);
        res.json(analytics);
    }
    catch (error) {
        logger_1.logger.error('Error fetching position analytics:', error);
        res.status(500).json({ error: 'Failed to fetch position analytics' });
    }
});
exports.default = router;
//# sourceMappingURL=positions.js.map