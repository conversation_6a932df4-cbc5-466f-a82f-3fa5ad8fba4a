"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const express_validator_1 = require("express-validator");
const databaseService_1 = require("../services/databaseService");
const logger_1 = require("../utils/logger");
const jwtService_1 = require("../services/jwtService");
const auditLogger_1 = require("../utils/auditLogger");
const crypto_1 = __importDefault(require("crypto"));
const router = express_1.default.Router();
const db = new databaseService_1.DatabaseService();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation Error',
            details: errors.array()
        });
    }
    next();
};
router.post('/login', [
    (0, express_validator_1.body)('email').isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('password').notEmpty().withMessage('Password is required')
], validateRequest, async (req, res) => {
    try {
        const { email, password } = req.body;
        const clientIp = req.ip || req.connection.remoteAddress;
        const userResult = await db.query(`
        SELECT
          u.id,
          u.email,
          u.password_hash,
          u.role,
          u.is_active,
          u.failed_login_attempts,
          u.locked_until,
          e.id as employee_id,
          pgp_sym_decrypt(e.first_name_encrypted, $2) as first_name,
          pgp_sym_decrypt(e.last_name_encrypted, $2) as last_name,
          e.status as employee_status
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        WHERE u.email = $1
      `, [email.toLowerCase(), process.env.ENCRYPTION_KEY || 'default-key']);
        if (userResult.rows.length === 0) {
            logger_1.loggers.auth.loginFailed(email, clientIp, 'User not found');
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid email or password'
            });
        }
        const user = userResult.rows[0];
        if (user.locked_until && new Date(user.locked_until) > new Date()) {
            logger_1.loggers.auth.loginFailed(email, clientIp, 'Account locked');
            return res.status(423).json({
                error: 'Account Locked',
                message: 'Account is temporarily locked due to multiple failed login attempts'
            });
        }
        if (!user.is_active) {
            logger_1.loggers.auth.loginFailed(email, clientIp, 'Account inactive');
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Account is not active'
            });
        }
        if (user.employee_id && user.employee_status !== 'active') {
            logger_1.loggers.auth.loginFailed(email, clientIp, 'Employee inactive');
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Employee account is not active'
            });
        }
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password_hash);
        if (!isPasswordValid) {
            const failedAttempts = (user.failed_login_attempts || 0) + 1;
            let lockUntil = null;
            if (failedAttempts >= 5) {
                lockUntil = new Date(Date.now() + 30 * 60 * 1000);
            }
            await db.query(`
          UPDATE users 
          SET failed_login_attempts = $1, locked_until = $2, updated_at = NOW()
          WHERE id = $3
        `, [failedAttempts, lockUntil, user.id]);
            logger_1.loggers.auth.loginFailed(email, clientIp, 'Invalid password');
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid email or password'
            });
        }
        await db.query(`
        UPDATE users 
        SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW(), updated_at = NOW()
        WHERE id = $1
      `, [user.id]);
        const userAgent = req.get('User-Agent') || '';
        const acceptLanguage = req.get('Accept-Language') || '';
        const acceptEncoding = req.get('Accept-Encoding') || '';
        const deviceFingerprint = jwtService_1.JWTService.generateDeviceFingerprint(userAgent, acceptLanguage, acceptEncoding);
        const sessionId = crypto_1.default.randomUUID();
        const permissions = await getUserPermissions(user.role);
        const tokenPair = await jwtService_1.jwtService.generateTokenPair(user.id, user.email, user.role, permissions, sessionId, deviceFingerprint, clientIp, userAgent);
        logger_1.loggers.auth.login(user.id, user.email, clientIp);
        await auditLogger_1.auditLogger.logAuthEvent('LOGIN', user.id, user.email, req, {
            sessionId,
            deviceFingerprint: deviceFingerprint.substring(0, 8) + '...',
            role: user.role
        });
        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                employeeId: user.employee_id,
                permissions
            },
            tokens: {
                accessToken: tokenPair.accessToken,
                refreshToken: tokenPair.refreshToken,
                expiresIn: tokenPair.expiresIn,
                tokenType: tokenPair.tokenType
            },
            sessionId,
            deviceFingerprint
        });
    }
    catch (error) {
        logger_1.logger.error('Login error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Login failed'
        });
    }
});
router.post('/refresh', [
    (0, express_validator_1.body)('refreshToken').notEmpty().withMessage('Refresh token is required'),
    (0, express_validator_1.body)('deviceFingerprint').optional().isString().withMessage('Device fingerprint must be a string')
], validateRequest, async (req, res) => {
    try {
        const { refreshToken, deviceFingerprint } = req.body;
        let currentDeviceFingerprint = deviceFingerprint;
        if (!currentDeviceFingerprint) {
            const userAgent = req.get('User-Agent') || '';
            const acceptLanguage = req.get('Accept-Language') || '';
            const acceptEncoding = req.get('Accept-Encoding') || '';
            currentDeviceFingerprint = jwtService_1.JWTService.generateDeviceFingerprint(userAgent, acceptLanguage, acceptEncoding);
        }
        const newTokenPair = await jwtService_1.jwtService.refreshAccessToken(refreshToken, currentDeviceFingerprint);
        logger_1.logger.info('Token refreshed successfully', {
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        res.json({
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: newTokenPair.accessToken,
                refreshToken: newTokenPair.refreshToken,
                expiresIn: newTokenPair.expiresIn,
                tokenType: newTokenPair.tokenType
            }
        });
    }
    catch (error) {
        logger_1.logger.warn('Token refresh failed:', {
            error: error.message,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        res.status(401).json({
            error: 'Unauthorized',
            message: 'Invalid or expired refresh token',
            code: 'REFRESH_FAILED'
        });
    }
});
router.get('/me', async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Access token required',
                code: 'TOKEN_REQUIRED'
            });
        }
        const token = authHeader.substring(7);
        const tokenPayload = await jwtService_1.jwtService.verifyAccessToken(token);
        const query = `
        SELECT
          u.id,
          u.email,
          u.role,
          u.employee_id,
          u.is_active,
          u.created_at,
          u.last_login,
          e.id as employee_id,
          pgp_sym_decrypt(e.first_name_encrypted, $2) as first_name,
          pgp_sym_decrypt(e.last_name_encrypted, $2) as last_name,
          e.status as employee_status
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        WHERE u.id = $1 AND u.is_active = true
      `;
        const result = await db.query(query, [tokenPayload.userId, process.env.ENCRYPTION_KEY || 'default-key']);
        if (result.rows.length === 0) {
            return res.status(404).json({
                error: 'User not found',
                message: 'User account not found or inactive',
                code: 'USER_NOT_FOUND'
            });
        }
        const user = result.rows[0];
        res.json({
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                employeeId: user.employee_id,
                isActive: user.is_active,
                createdAt: user.created_at,
                lastLogin: user.last_login,
                permissions: tokenPayload.permissions
            },
            sessionId: tokenPayload.sessionId
        });
    }
    catch (error) {
        logger_1.logger.warn('Get user profile failed:', {
            error: error.message,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        if (error.message.includes('expired') || error.message.includes('invalid')) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'Invalid or expired access token',
                code: 'TOKEN_INVALID'
            });
        }
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to get user profile',
            code: 'PROFILE_FETCH_FAILED'
        });
    }
});
router.post('/logout', [
    (0, express_validator_1.body)('refreshToken').optional().isString(),
    (0, express_validator_1.body)('sessionId').optional().isString()
], async (req, res) => {
    try {
        const { refreshToken, sessionId } = req.body;
        const authHeader = req.headers.authorization;
        let userId = null;
        let userEmail = null;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            try {
                const tokenPayload = await jwtService_1.jwtService.verifyAccessToken(token);
                userId = tokenPayload.userId;
                userEmail = tokenPayload.email;
                await jwtService_1.jwtService.revokeAccessToken(token);
                if (sessionId || tokenPayload.sessionId) {
                    await jwtService_1.jwtService.revokeAllTokens(sessionId || tokenPayload.sessionId);
                }
                await auditLogger_1.auditLogger.logAuthEvent('LOGOUT', userId, userEmail, req, { sessionId: sessionId || tokenPayload.sessionId });
                logger_1.loggers.auth.logout(userId, userEmail);
            }
            catch (error) {
                logger_1.logger.warn('Invalid token during logout:', error.message);
            }
        }
        if (refreshToken) {
            try {
                const decoded = jsonwebtoken_1.default.verify(refreshToken, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET);
                if (decoded.tokenId) {
                    await jwtService_1.jwtService.revokeRefreshToken(decoded.tokenId);
                }
            }
            catch (error) {
                logger_1.logger.warn('Invalid refresh token during logout:', error.message);
            }
        }
        res.json({
            message: 'Logout successful',
            code: 'LOGOUT_SUCCESS'
        });
    }
    catch (error) {
        logger_1.logger.error('Logout error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Logout failed',
            code: 'LOGOUT_ERROR'
        });
    }
});
router.post('/forgot-password', [
    (0, express_validator_1.body)('email').isEmail().withMessage('Valid email is required')
], validateRequest, async (req, res) => {
    try {
        const { email } = req.body;
        const userResult = await db.query(`
        SELECT id, email, first_name, last_name FROM users WHERE email = $1 AND is_active = true
      `, [email.toLowerCase()]);
        if (userResult.rows.length === 0) {
            return res.json({
                message: 'If an account with that email exists, a password reset link has been sent.'
            });
        }
        const user = userResult.rows[0];
        const resetToken = jsonwebtoken_1.default.sign({ userId: user.id, type: 'password_reset' }, process.env.JWT_SECRET, { expiresIn: '1h' });
        await db.query(`
        INSERT INTO password_reset_tokens (user_id, token, expires_at)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id) 
        DO UPDATE SET token = $2, expires_at = $3, created_at = NOW()
      `, [user.id, resetToken, new Date(Date.now() + 60 * 60 * 1000)]);
        logger_1.logger.info('Password reset requested', { userId: user.id, email: user.email });
        res.json({
            message: 'If an account with that email exists, a password reset link has been sent.'
        });
    }
    catch (error) {
        logger_1.logger.error('Forgot password error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Password reset request failed'
        });
    }
});
router.post('/reset-password', [
    (0, express_validator_1.body)('token').notEmpty().withMessage('Reset token is required'),
    (0, express_validator_1.body)('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
], validateRequest, async (req, res) => {
    try {
        const { token, password } = req.body;
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        if (decoded.type !== 'password_reset') {
            return res.status(400).json({
                error: 'Bad Request',
                message: 'Invalid reset token'
            });
        }
        const tokenResult = await db.query(`
        SELECT user_id FROM password_reset_tokens 
        WHERE user_id = $1 AND token = $2 AND expires_at > NOW()
      `, [decoded.userId, token]);
        if (tokenResult.rows.length === 0) {
            return res.status(400).json({
                error: 'Bad Request',
                message: 'Invalid or expired reset token'
            });
        }
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
        const passwordHash = await bcryptjs_1.default.hash(password, saltRounds);
        await db.transaction([
            {
                text: 'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
                params: [passwordHash, decoded.userId]
            },
            {
                text: 'DELETE FROM password_reset_tokens WHERE user_id = $1',
                params: [decoded.userId]
            },
            {
                text: 'DELETE FROM user_sessions WHERE user_id = $1',
                params: [decoded.userId]
            }
        ]);
        logger_1.logger.info('Password reset completed', { userId: decoded.userId });
        res.json({
            message: 'Password reset successful'
        });
    }
    catch (error) {
        logger_1.logger.error('Reset password error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: 'Password reset failed'
        });
    }
});
async function getUserPermissions(role) {
    const rolePermissions = {
        'super_admin': [
            'super_admin', 'system_admin', 'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll', 'it'
        ],
        'hr_admin': [
            'hr_admin', 'hr', 'manager', 'employee', 'recruiter', 'payroll'
        ],
        'hr': [
            'hr', 'employee', 'recruiter'
        ],
        'manager': [
            'manager', 'employee'
        ],
        'employee': [
            'employee'
        ]
    };
    return rolePermissions[role] || ['employee'];
}
exports.default = router;
//# sourceMappingURL=auth.js.map