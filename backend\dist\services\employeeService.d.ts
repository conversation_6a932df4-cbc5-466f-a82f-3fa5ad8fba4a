export interface Employee {
    id: string;
    employeeId?: string;
    firstName: string;
    lastName: string;
    email: string;
    personalEmail?: string;
    phone?: string;
    dateOfBirth?: string;
    gender?: string;
    address?: any;
    emergencyContact?: any;
    departmentId: string;
    departmentName?: string;
    positionId: string;
    positionTitle?: string;
    managerId?: string;
    employeeType: string;
    hireDate: string;
    salary?: number;
    currency?: string;
    workLocation?: string;
    skills?: any[];
    certifications?: any[];
    status: string;
    createdAt: string;
    updatedAt: string;
}
export declare class EmployeeService {
    private db;
    private encryptionKey;
    constructor();
    private decryptEmployeeData;
    getEmployees(filters: any, user: any): Promise<any>;
    getEmployeeById(employeeId: string, user: any): Promise<Employee | null>;
    createEmployee(employeeData: any, createdBy: string): Promise<Employee>;
    updateEmployee(employeeId: string, updateData: any, user: any): Promise<Employee | null>;
    deactivateEmployee(employeeId: string, deactivatedBy: string): Promise<boolean>;
    getEmployeeProfile(employeeId: string, user: any): Promise<any>;
    private hasEmployeeAccess;
    private getAllowedUpdateFields;
    private getColumnName;
    getEmployeeTeam(employeeId: string, user: any): Promise<any>;
    getDirectReports(employeeId: string, user: any): Promise<any>;
    addEmployeeSkills(employeeId: string, skills: any[], user: any): Promise<any>;
    getEmployeeAnalytics(filters: any, user: any): Promise<any>;
    searchEmployees(filters: any, user: any): Promise<any>;
    getManagers(user: any): Promise<Array<{
        id: string;
        name: string;
        departmentId: string;
    }>>;
}
//# sourceMappingURL=employeeService.d.ts.map