"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { ThemeMode, getThemeMode, setThemeMode, applyTheme, initializeTheme } from '@/lib/theme'

interface ThemeContextType {
  theme: ThemeMode
  setTheme: (theme: ThemeMode) => void
  toggleTheme: () => void
  systemTheme: 'light' | 'dark'
  actualTheme: 'light' | 'dark' // The actual theme being applied (resolved from system if needed)
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    // Provide a fallback when used outside of ThemeProvider
    return {
      theme: 'system' as ThemeMode,
      setTheme: () => {},
      toggleTheme: () => {},
      systemTheme: 'light' as 'light' | 'dark',
      actualTheme: 'light' as 'light' | 'dark'
    }
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: ThemeMode
}

export function ThemeProvider({ children, defaultTheme = 'light' }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<ThemeMode>(defaultTheme)
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light')
  const [mounted, setMounted] = useState(false)

  // Get the actual theme being applied
  const actualTheme = theme === 'system' ? systemTheme : theme

  useEffect(() => {
    // Initialize theme on mount
    const storedTheme = getThemeMode()
    setThemeState(storedTheme)

    // Detect system theme
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light')

    // Set mounted first to prevent hydration mismatch
    setMounted(true)

    // Note: Theme is already applied by the script in root layout
    // We don't need to apply it again here to prevent hydration issues

    // Listen for system theme changes
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
      // If using system theme, reapply it
      if (getThemeMode() === 'system') {
        applyTheme('system')
      }
    }

    mediaQuery.addEventListener('change', handleSystemThemeChange)

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange)
    }
  }, [])

  const handleSetTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme)
    setThemeMode(newTheme)
    applyTheme(newTheme)
  }

  const handleToggleTheme = () => {
    const currentActualTheme = theme === 'system' ? systemTheme : theme
    const newTheme = currentActualTheme === 'light' ? 'dark' : 'light'
    handleSetTheme(newTheme)
  }

  // Note: We don't prevent rendering to avoid hydration mismatch
  // The theme is already applied by the script in root layout

  const value: ThemeContextType = {
    theme,
    setTheme: handleSetTheme,
    toggleTheme: handleToggleTheme,
    systemTheme,
    actualTheme,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
