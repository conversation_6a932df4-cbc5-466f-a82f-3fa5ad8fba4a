// Role utility functions for formatting and display

import { UserRole } from './auth'

/**
 * Format role for display in UI
 * Converts technical role names to user-friendly display names
 */
export function formatRole(role: UserRole | string | null | undefined): string {
  const roleMap: Record<string, string> = {
    'super_admin': 'Super Admin',
    'system_admin': 'System Admin',
    'hr_admin': 'HR Admin',
    'hr': 'HR',
    'manager': 'Manager',
    'employee': 'Employee',
    'recruiter': 'Recruiter',
    'payroll': 'Payroll',
    'it': 'IT'
  }
  
  if (!role) return 'Employee'
  return roleMap[role] || 'Employee'
}

/**
 * Get role color for UI styling
 * Returns appropriate color classes for different roles
 */
export function getRoleColor(role: UserRole | string | null | undefined): string {
  const colorMap: Record<string, string> = {
    'super_admin': 'text-red-600 bg-red-50 border-red-200',
    'system_admin': 'text-purple-600 bg-purple-50 border-purple-200',
    'hr_admin': 'text-blue-600 bg-blue-50 border-blue-200',
    'hr': 'text-green-600 bg-green-50 border-green-200',
    'manager': 'text-orange-600 bg-orange-50 border-orange-200',
    'employee': 'text-gray-600 bg-gray-50 border-gray-200',
    'recruiter': 'text-indigo-600 bg-indigo-50 border-indigo-200',
    'payroll': 'text-yellow-600 bg-yellow-50 border-yellow-200',
    'it': 'text-cyan-600 bg-cyan-50 border-cyan-200'
  }
  
  if (!role) return colorMap['employee']
  return colorMap[role] || colorMap['employee']
}

/**
 * Get role badge component props
 * Returns formatted role name and styling for badge display
 */
export function getRoleBadgeProps(role: UserRole | string | null | undefined) {
  return {
    text: formatRole(role),
    className: getRoleColor(role)
  }
}

/**
 * Check if role is admin level (hr_admin, system_admin, super_admin)
 */
export function isAdminRole(role: UserRole | string | null | undefined): boolean {
  const adminRoles = ['super_admin', 'system_admin', 'hr_admin']
  return role ? adminRoles.includes(role) : false
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: UserRole | string | null | undefined): number {
  const roleLevels: Record<string, number> = {
    'employee': 1,
    'recruiter': 2,
    'payroll': 2,
    'it': 2,
    'hr': 3,
    'manager': 4,
    'hr_admin': 5,
    'system_admin': 6,
    'super_admin': 7
  }
  
  if (!role) return 1
  return roleLevels[role] || 1
}

/**
 * Compare if first role has higher or equal level than second role
 */
export function hasRoleLevel(userRole: UserRole | string | null | undefined, requiredRole: UserRole | string): boolean {
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole)
}
